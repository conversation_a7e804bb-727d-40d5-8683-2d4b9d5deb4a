Yaf入门项目

## 涉及技术
#### 前端
- requirejs:模块化和异步加载
- bootstrap

#### 后端
- smarty/twig:php模板引擎,默认关闭
- medoo:orm数据库半框架,library/Model.php

## 快速开始
可以按照以下步骤来部署和运行程序(SAE已经内置，不需要自己安装):
```
1.请确保机器localhost已经安装了Yaf扩展框架, 并且已经启动服务器和PHP;
2.把这个项目拷贝到Webserver的DocumentRoot目录下;
3.配置PHP扩展 extension=yaf.so
4.导入yaf_init.sql,并确保conf/application.ini中,mysql的host,user,pwd正确配置.
5.重启Webserver;
6.访问http://yourhost/,出现网站页面!, 表示运行成功,否则请查看错误日志;
```


### 目录结构

对于Yaf的应用, 都应该遵循类似下面的目录结构.

本项目的目录说明
```
+ application
  |+ common //公共类库
  |+ controllers //控制器
  |+ models //model目录
  |+ library //本地类库
  |+ plugins //插件目录
  |+ views //视图
  |- Bootstrap.php //项目的全局配置,包括路由和memcached的配置等
  |- yaf_classes.php //yaf框架的函数列表,方便补全
+ conf
  |- application.ini //配置文件
+ public
  |- index.php //入口文件
  |- .htaccess //重写规则
  |+ css
  |+ img
  |+ js
+ runtime //插件目录和全局配置
```

参考
===
- [yaf的一些资源](http://www.laruence.com/2012/07/06/2649.html)
- [yaf学习的一些思路](http://achun.iteye.com/blog/1473126)
- [YafUse项目](https://www.github.com/melonwool/YafUse/)
- [yaf的api](http://yaf.laruence.com/manual/index.html)
- [浅谈数据库设计技巧](http://www.knowsky.com/4937.html)
- [SAE(sina app engine)](http://sae.sina.com.cn)
