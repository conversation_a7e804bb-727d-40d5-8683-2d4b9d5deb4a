<?php

/**
 * @name Bootstrap
 * @desc 所有在Bootstrap类中, 以_init开头的方法, 都会被Yaf调用,
 * @see http://www.php.net/manual/en/class.yaf-bootstrap-abstract.php
 * 这些方法, 都接受一个参数:Yaf_Dispatcher $dispatcher
 * 调用的次序, 和申明的次序相同
 */
class Bootstrap extends Yaf_Bootstrap_Abstract
{

	private $_config;

	/**
	 * 初始化配置文件
	 *
	 * @return void
	 */
	public function _initConfig()
	{
		$this->_config = Yaf_Application::app()->getConfig();
		Yaf_Registry::set('config', $this->_config);
	}

	public function _initPlugin(Yaf_Dispatcher $dispatcher)
	{
		$AutoloadPlugin = new AutoloadPlugin();
		$dispatcher->registerPlugin($AutoloadPlugin);
	}

	public function _initRoute(Yaf_Dispatcher $dispatcher)
	{
		//在这里注册自己的路由协议,默认使用简单路由
		Yaf_Dispatcher::getInstance()->getRouter()->addRoute(
			"supervar",
			new Yaf_Route_Supervar("r")
		);
		Yaf_Dispatcher::getInstance()->getRouter()->addRoute(
			"simple",
			new Yaf_Route_simple('m', 'c', 'a')
		);
		//加载路由配置
		Yaf_Loader::import(APP_PATH . "/routes/Routes.php");
		Yaf_Dispatcher::getInstance()->getRouter()->addConfig(new Yaf_Config_Simple($config));
	}

	public function _initView(Yaf_Dispatcher $dispatcher)
	{
		//在这里注册自己的view控制器，例如smarty,firekylin
		Yaf_Dispatcher::getInstance()->disableView(); //关闭其自动渲染
	}

	public function _initSmarty(Yaf_Dispatcher $dispatcher)
	{
		Yaf_Loader::import("php/smarty/Adapter.php");
		$smarty = new Smarty_Adapter(null, Yaf_Registry::get("config")->get("smarty"));
		Yaf_Dispatcher::getInstance()->setView($smarty);
	}

	/*
	public function _initSmarty(Yaf_Dispatcher $dispatcher)
	{
		// init smarty view engine
		Yaf_Loader::import("php/smarty/Adapter.php");
		$smarty = new Smarty_Adapter(null, $this->_config->smarty);
		$dispatcher->setView($smarty);
	}

	public function _initView(Yaf_Dispatcher $dispatcher)
	{
		Yaf_Registry::set('dispatcher', $dispatcher);
	}
	*/

	public function _initRedis(Yaf_Dispatcher $dispatcher)
	{
		$this->_YRedis = new YRedis();
		Yaf_Registry::set('redis', $this->_YRedis);
	}

	/**
	 * 站点信息
	 * @param  Yaf_Dispatcher $dispatcher [description]
	 * @return [type]                     [description]
	 */
	public function _initSite(Yaf_Dispatcher $dispatcher)
	{
		$site = [
			'site_code' => 'jst',
		];
		Yaf_Registry::set('site', $site);
		//站点信息
		Yaf_Registry::set('site_code', $site['site_code']);

		//模板目录
		$_isMobile = $this->isMobile() === true ? 'm' : '';
		Yaf_Registry::set('isMobile', $_isMobile);
		Yaf_Registry::set('template_dir', $site['site_code'] . $_isMobile);
	}

	/**
	 * 初始化session
	 * @param  Yaf_Dispatcher $dispatcher [description]
	 * @return [type]                     [description]
	 */
	public function _initSession(Yaf_Dispatcher $dispatcher)
	{
		if ($this->_YRedis->ping() === true) {
			//为了防止WEB集群下SESSION冲撞问题，特此设置前缀区分。
			$prefix      = Yaf_Registry::get('site_code') . '_';
			$this->_sess = new Session($this->_YRedis->_REDIS, 86400, $prefix);

			session_set_save_handler($this->_sess);
		}

		session_start();
	}

	/*
	 * 移动端判断
	 */
	private function isMobile()
	{
		// 如果有HTTP_X_WAP_PROFILE则一定是移动设备
		if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
			return true;
		}
		// 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
		if (isset($_SERVER['HTTP_VIA'])) {
			// 找不到为flase,否则为true
			return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
		}
		// 脑残法，判断手机发送的客户端标志,兼容性有待提高
		if (isset($_SERVER['HTTP_USER_AGENT'])) {
			$clientkeywords = array(
				'nokia',
				'sony',
				'ericsson',
				'mot',
				'samsung',
				'htc',
				'sgh',
				'lg',
				'sharp',
				'sie-',
				'philips',
				'panasonic',
				'alcatel',
				'lenovo',
				'iphone',
				'ipod',
				'blackberry',
				'meizu',
				'android',
				'netfront',
				'symbian',
				'ucweb',
				'windowsce',
				'palm',
				'operamini',
				'operamobi',
				'openwave',
				'nexusone',
				'cldc',
				'midp',
				'wap',
				'mobile',
			);
			// 从HTTP_USER_AGENT中查找手机浏览器的关键字
			if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
				return true;
			}
		}
		// 协议法，因为有可能不准确，放到最后判断
		if (isset($_SERVER['HTTP_ACCEPT'])) {
			// 如果只支持wml并且不支持html那一定是移动设备
			// 如果支持wml和html但是wml在html之前则是移动设备
			if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'], 'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html')))) {
				return true;
			}
		}
		return false;
	}

	/*protected function _initTwig(Yaf_Dispatcher $dispatcher)
	{
	Yaf_Loader::import("php/twig/Adapter.php");
	$view = new Twig_Adapter(APP_PATH.'/application/views', $this->_config->twig->toArray());
	$dispatcher->setView($view);
	}*/

	/*public function _initLocalName() {
	Yaf_Loader::getInstance()->registerLocalNamespace(array( 'Smarty',));
	//申明, 凡是以Zend,Local开头的类, 都是本地类
	Yaf\Loader::getInstance()->registerLocalNameSpace(array("Zend", "Local"));
	}*/
}
