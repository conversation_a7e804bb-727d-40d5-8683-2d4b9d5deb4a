<?php
/**
 * @file            Functions.php
 * @version         1.0
 * @date            Thu, 26 Oct 2017 11:34:46 GMT
 * @description     基础函数库
 */

/**
  +----------------------------------------------------------
 * M函数用于实例化一个没有模型文件的Model
  +----------------------------------------------------------
 * @param string name Model名称 支持指定基础模型 例如 MongoModel:User
 * @param string tablePrefix 表前缀
 * @param mixed $connection 数据库连接信息
  +----------------------------------------------------------
 * @return Model
  +----------------------------------------------------------
 */
function M($name = '', $tablePrefix = '', $connection = '') {
    static $_model = array();
    if(strpos($name,':')) {
        list($class,$name) = explode(':',$name);
    }else{
        $class = 'Model';
    }
    if (!isset($_model[$name.$class])) {
        $_model[$name.$class] = new $class($name, $tablePrefix, $connection);
    }
    return $_model[$name.$class];
}

/**
  +----------------------------------------------------------
 * D函数用于实例化Model 格式 项目://分组/模块
 +----------------------------------------------------------
 * @param string name Model资源地址
  +----------------------------------------------------------
 * @return Model
  +----------------------------------------------------------
 */
function D($name = '') {
    if(empty($name)) return new Model;
    static $_model = array();
    if(isset($_model[$name]))
        return $_model[$name];
    if(strpos($name, '://')) {// 指定项目
        $name = str_replace('://', '/models/', $name);
    }else{
        $name = Yaf_Application::app()->getConfig()->application->directory.'/models/'.$name;
    }
    $class = basename($name.'Model');
    if(class_exists($class)) {
        $model = new $class();
    }else {
        $model = new Model(basename($name));
    }
    return $model;
}

/*
 * 添加和获取页面Trace记录
 */
function trace($value='[yaf]', $label='', $level='DEBUG') {
    if('[yaf]' === $value){ // 获取trace信息
        return $_trace;
    }else{
        $info = ($label?$label.':':'').print_r($value,true);
        if('ERR' == $level) {// 抛出异常
            throw new Exception($info);
        }
        $level = strtoupper($level);
        if(!isset($_trace[$level])) {
            $_trace[$level] = array();
        }
        $_trace[$level][] = $info;
    }
}

/*
 * 数据加密
 */
function encrypt($str, $toBase64 = false, $key = "encryptKey"){
    $r = md5($key);
    $c = 0;
    $v = "";
    $len = strlen($str);
    $l = strlen($r);
    for ($i=0; $i<$len; $i++){
      if ($c== $l) $c=0;
      $v .= substr($r,$c,1) . (substr($str,$i,1) ^ substr($r,$c,1));
      $c++;
    }
    if($toBase64) {
        return base64_encode(_ed($v,$key));
    }else {
        return _ed($v,$key);
    }
}

/*
 * 数据解密
 */
function decrypt($str, $toBase64 = false, $key = "encryptKey") {
    if($toBase64) {
        $str = _ed(base64_decode($str),$key);
    }else {
        $str = _ed($str,$key);
    }
    $v = "";
    $len = strlen($str);
    for ($i=0; $i<$len; $i++){
      $md5 = substr($str,$i,1);
      $i++;
      $v.= (substr($str,$i,1) ^ $md5);
    }
    return $v;
}

function _ed($str, $key = "encryptKey") {
    $r = md5($key);
    $c = 0;
    $v = "";
    $len = strlen($str);
    $l = strlen($r);
    for ($i=0; $i<$len; $i++) {
       if ($c==$l) $c=0;
       $v .= substr($str,$i,1) ^ substr($r,$c,1);
       $c++;
    }
    return $v;
}

/**
 * 浏览器友好的变量输出
 * @param mixed $var 变量
 * @param boolean $echo 是否输出 默认为True 如果为false 则返回输出字符串
 * @param string $label 标签 默认为空
 * @param boolean $strict 是否严谨 默认为true
 * @return void|string
 */
function dump($var, $echo = true, $label = null, $strict = true)
{
    $label = ($label === null) ? '' : rtrim($label) . ' ';
    if (!$strict) {
        if (ini_get('html_errors')) {
            $output = print_r($var, true);
            $output = '<pre>' . $label . htmlspecialchars($output, ENT_QUOTES) . '</pre>';
        } else {
            $output = $label . print_r($var, true);
        }
    } else {
        ob_start();
        var_dump($var);
        $output = ob_get_clean();
        if (!extension_loaded('xdebug')) {
            $output = preg_replace('/\]\=\>\n(\s+)/m', '] => ', $output);
            $output = '<pre>' . $label . htmlspecialchars($output, ENT_QUOTES) . '</pre>';
        }
    }
    if ($echo) {
        echo ($output);
        return null;
    } else {
        return $output;
    }
}

/*
 * json返回
 * */
function jsonReturn($data, $json_option = 0)
{
    // 返回JSON数据格式到客户端 包含状态信息
    header('Content-Type:application/json; charset=utf-8');
    exit(json_encode($data, $json_option));
}