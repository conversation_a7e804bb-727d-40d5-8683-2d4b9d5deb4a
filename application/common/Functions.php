<?php

/**
 * @file            Functions.php
 * @version         1.0
 * @date            Thu, 26 Oct 2017 11:34:46 GMT
 * @description     公共函数库
 */
/**
 * 返回页面
 */
function get_back_url($default_url = null)
{
    if (isset($_REQUEST['back_url']) && !empty($_REQUEST['back_url'])) {
        $back_url = xss_clean($_REQUEST['back_url']);
    } else {
        if (isset($_SERVER['HTTP_REFERER']) && !empty($_REQUEST['back_url'])) {
            $back_url = $_SERVER['HTTP_REFERER'];
        } else {
            $back_url = $default_url;
        }
    }
    // $back_url = empty($_REQUEST['back_url']) ? $_SERVER['HTTP_REFERER'] : $_REQUEST['back_url'];
    if (strpos($back_url, "login")) {
        $back_url = $default_url;
    }
    return $back_url;
}

/**
 * 请求接口
 * @param  [type] $params [description]
 * @return [type]         [description]
 */
function requestApi($params, $api_name)
{
    $appid = Yaf_Registry::get("config")->titan_tool_api_config->appid;
    $key = Yaf_Registry::get("config")->titan_tool_api_config->key;
    $url = Yaf_Registry::get("config")->titan_tool_api_config->host . $api_name;

    $params_en = json_encode($params);
    $sign = strtoupper(md5($appid . $params_en . $key));
    $p_data = array(
        'header' => array(
            'appid' => $appid,
            'sign' => $sign,
        ),
        'body' => $params_en
    );
    $encry_data = base64_encode(json_encode($p_data));
    $send = HttpCurl::PostUrl($url, $encry_data);
    $result = json_decode($send, true);

    return $result;
}

/**
 * 发送短信上海通道
 * @param $mobile string 手机号
 * @param $content string 内容（code｜content｜text）
 * @param $sms_type = 'titan_yx' //营销短信，必须加：拒收请回复R
 * @param $sms_type = 'titan_tz' //通知短信，发送验证码，支付短链
 * @return array
 */
function BJGW_SMS_SH($mobile, $content, $sms_type = 'titan_tz')
{
    $site = Yaf_Registry::get('site')['site_id'];
    if ($site == '101') {
        $contentFrom = "【宽带通】" . $content;
    } else {
        $contentFrom = "【长城宽带】" . $content;
    }
    $params = [
        "mobile" => $mobile,
        "sms_type" => $sms_type,
        "msg" => $contentFrom
    ];
    $res = requestApi($params, 'SendSms');
    if ($res['header']['code'] != 200) {
        return ['status' => 400, 'info' => '发送失败', 'msg' => $res['header']['desc']];
    }
    return ['status' => 100, 'info' => '发送成功', 'msg' => []];
}

/**
 * 发送短信大麦通道
 * @param [type] $mobile  [description]
 * @param [type] $content [description]
 */
function BJGW_SMS_DM($mobile, $content)
{
    $url = "http://**************:8000/v2/sms/send";
    $site = Yaf_Registry::get('site')['site_id'];
    $timestamp = date('YmdHis', time());
    if ($site == '101') {
        $account = "KDT001";
        $password = "6673mq9W";
        $contentFrom = "【宽带通】" . $content;
    } else {
        $account = "KDT001";
        $password = "6673mq9W";
        $contentFrom = "【长城宽带】" . $content;
    }
    $token = sha1($account . $password . $timestamp);
    $params = "account=$account&token=$token&ts=$timestamp&dest=$mobile&content=$contentFrom&ref=&ext=";
    $send = HttpCurl::PostUrl($url, $params);
    $result = json_decode($send, true);
    if ($result['status'] == 0) {
        $data['status'] = 100;
    } else {
        $data['status'] = 400;
    }
    return $data;
}

/**
 * 获取客户端IP地址
 */
function get_client_ip()
{
    static $ip = NULL;
    if ($ip !== NULL) return $ip;
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        $pos =  array_search('unknown', $arr);
        if (false !== $pos) unset($arr[$pos]);
        $ip   =  trim($arr[0]);
    } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    // IP地址合法验证
    $ip = (false !== ip2long($ip)) ? $ip : '0.0.0.0';
    return $ip;
}

/**
 * 生成唯一的单据ID
 */
function getUUID()
{
    $fix = get_source()['prefix'];
    $time = time();
    return $fix . '1' . date("ymd") . sprintf('%05d', (date('s', $time) + 60 * date('i', $time) + 3600 * date('H', $time))) . substr(microtime(), 5, 2) . rand(10, 99);
}

/***
 * 来源
 * prefix 生成订单前缀
 * source 来源 1 pc 2 M站
 * type 类型
 */
function get_source()
{
    $fix = array();
    switch (Yaf_Registry::get('template_dir')) {
        case 'jst':
            $fix = array("prefix" => 'JSTW', 'source' => '1', 'type' => 'jst');
            break;
        case 'jstm':
            $fix = array("prefix" => 'JSTM', 'source' => '2', 'type' => 'jstm');
            break;
    }
    return $fix;
}

/*
 * 移动端判断
 */
function isMobile()
{
    // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
    if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
        return true;
    }
    // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
    if (isset($_SERVER['HTTP_VIA'])) {
        // 找不到为flase,否则为true
        return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
    }
    // 脑残法，判断手机发送的客户端标志,兼容性有待提高
    if (isset($_SERVER['HTTP_USER_AGENT'])) {
        $clientkeywords = array(
            'nokia',
            'sony',
            'ericsson',
            'mot',
            'samsung',
            'htc',
            'sgh',
            'lg',
            'sharp',
            'sie-',
            'philips',
            'panasonic',
            'alcatel',
            'lenovo',
            'iphone',
            'ipod',
            'blackberry',
            'meizu',
            'android',
            'netfront',
            'symbian',
            'ucweb',
            'windowsce',
            'palm',
            'operamini',
            'operamobi',
            'openwave',
            'nexusone',
            'cldc',
            'midp',
            'wap',
            'mobile'
        );
        // 从HTTP_USER_AGENT中查找手机浏览器的关键字
        if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
            return true;
        }
    }
    // 协议法，因为有可能不准确，放到最后判断
    if (isset($_SERVER['HTTP_ACCEPT'])) {
        // 如果只支持wml并且不支持html那一定是移动设备
        // 如果支持wml和html但是wml在html之前则是移动设备
        if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'], 'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html')))) {
            return true;
        }
    }
    return false;
}

/**
 * 订单状态
 */
function order_status($status = null, $field = null)
{
    $list = array(
        1 => array('key' => '1', 'val' => '未支付', 'tag' => 'danger'),
        5 => array('key' => '5', 'val' => '已支付', 'tag' => 'navy'),
        6 => array('key' => '6', 'val' => '线下收款', 'tag' => 'navy'),
        7 => array('key' => '7', 'val' => '申请退款', 'tag' => 'warning'),
        9 => array('key' => '9', 'val' => '已退款', 'tag' => 'danger'),
    );
    if (empty($status) and empty($field)) {
        return $list;
    } else {
        if (empty($field)) {
            $field = 'val';
        }
        return $list[$status][$field];
    }
}

/**
 * 订单施工状态
 */
function work_status($status = null, $field = null)
{
    $list = array(
        1 => array('key' => '1', 'val' => '未处理', 'tag' => 'warning'),
        3 => array('key' => '3', 'val' => '处理中', 'tag' => 'success'),
        4 => array('key' => '4', 'val' => '关闭', 'tag' => 'danger'),
        5 => array('key' => '5', 'val' => '已派工', 'tag' => 'success'),
        9 => array('key' => '9', 'val' => '已完成', 'tag' => 'navy'),
    );
    if (empty($status) and empty($field)) {
        return $list;
    } else {
        if (empty($field)) {
            $field = 'val';
        }
        return $list[$status][$field];
    }
}

/**
 * 验证表单重复
 */
function checkFormToken($action)
{
    header("Content-type: text/html; charset=utf-8");
    if (isset($_REQUEST['form_token']) && !empty($_REQUEST['form_token']) && isset($_SESSION["form_token_" . $action])) {
        if ($_REQUEST['form_token'] != $_SESSION["form_token_" . $action]) {
            throw new Exception("请不要刷新本页面或重复提交表单");
            exit();
        }
    }
}

/**
 * 设定formID
 * 后续的升级办法：将token按照array的形式存在session中
 */
function initFormToken($action)
{
    $uuid = guid();
    $_SESSION["form_token_" . $action] = $uuid;
    return $uuid;
}

function destoryFormToken($action)
{
    unset($_SESSION["form_token_" . $action]);
}

function guid()
{
    if (function_exists('com_create_guid')) {
        return com_create_guid();
    } else {
        mt_srand((float)microtime() * 10000); //optional for php 4.2.0 and up.
        $charid = strtoupper(md5(uniqid(rand(), true)));
        $hyphen = ''; //chr(45);// "-"
        $uuid = substr($charid, 0, 8) . $hyphen
            . substr($charid, 8, 4) . $hyphen
            . substr($charid, 12, 4) . $hyphen
            . substr($charid, 16, 4) . $hyphen
            . substr($charid, 20, 12);
        return $uuid;
    }
}

//请求防SQL注入
function requestdecode($request)
{
    if (empty($request)) {
        return;
    }
    $request_arr = array();
    if (is_array($request)) {
        $keys = array_keys($request);
        for ($i = 0; $i < count($keys); $i++) {
            if ($keys[$i] == 's') {
                $request_arr['s'] = $request[$keys[$i]];
            } else {
                $key = paramdecode($keys[$i]);
                $value = paramdecode($request[$keys[$i]]);
                $request_arr[$key] = $value;
            }
        }
    }

    return $request_arr;
}

function paramdecode($str)
{
    if (empty($str)) return;
    if ($str == "") return $str;
    $str = str_replace("&", chr(34), $str);
    $str = str_replace(">", "", $str);
    $str = str_replace("<", "", $str);
    $str = str_replace("&", "&", $str);
    $str = str_replace("%", " ", $str);
    $str = str_replace(" ", chr(32), $str);
    $str = str_replace(" ", chr(9), $str);
    $str = str_replace("'", chr(39), $str);
    $str = str_replace("<br />", chr(13), $str);
    $str = str_replace("''", "'", $str);
    $str = str_replace("select", "", $str);
    $str = str_replace("join", "", $str);
    $str = str_replace("union", "", $str);
    $str = str_replace("where", "", $str);
    $str = str_replace("insert", "", $str);
    $str = str_replace("delete", "", $str);
    $str = str_replace("update", "", $str);
    $str = str_replace("like", "", $str);
    $str = str_replace("drop", "", $str);
    $str = str_replace("create", "", $str);
    $str = str_replace("modify", "", $str);
    $str = str_replace("rename", "", $str);
    $str = str_replace("alter", "", $str);
    $str = str_replace("cas", "", $str);
    return $str;
}

/**
 * 过滤掉emoji表情
 * @param $str
 * @return mixed
 */
function filterEmoji($str)
{
    $str = preg_replace_callback(
        '/./u',
        function (array $match) {
            return strlen($match[0]) >= 4 ? '' : $match[0];
        },
        $str
    );

    return $str;
}

/**
 * 请求接口方法
 * @param $url
 * @param $params
 * @return bool|mixed
 */
function requestGwbnApi($url, $params)
{
    if (!empty($params) && !empty($url)) {
        $appid = Yaf_Registry::get('config')['gwbn_api_appid'];
        $url = Yaf_Registry::get('config')['gwbn_api_url'] . "/" . $url;
        $key = Yaf_Registry::get('config')['gwbn_api_key'];
        $params_en = json_encode($params);
        $sign = strtoupper(md5($appid . $params_en . $key));
        $p_data = array(
            'header' => array(
                'appid' => $appid,
                'sign' => $sign,
            ),
            'body' => $params_en
        );
        $encry_data = base64_encode(json_encode($p_data));
        $send = HttpCurl::PostUrl($url, $encry_data);
        $result = json_decode($send, true);
        return $result;
    } else {
        return false;
    }
}

/**
 * 判断是否为合法的身份证号码
 * @param $mobile
 * @return int
 */
function isCreditNo($vStr)
{
    $vCity = array(
        '11',
        '12',
        '13',
        '14',
        '15',
        '21',
        '22',
        '23',
        '31',
        '32',
        '33',
        '34',
        '35',
        '36',
        '37',
        '41',
        '42',
        '43',
        '44',
        '45',
        '46',
        '50',
        '51',
        '52',
        '53',
        '54',
        '61',
        '62',
        '63',
        '64',
        '65',
        '71',
        '81',
        '82',
        '91'
    );
    if (!preg_match('/^([\d]{17}[xX\d]|[\d]{15})$/', $vStr)) return false;
    if (!in_array(substr($vStr, 0, 2), $vCity)) return false;
    $vStr = preg_replace('/[xX]$/i', 'a', $vStr);
    $vLength = strlen($vStr);
    if ($vLength == 18) {
        $vBirthday = substr($vStr, 6, 4) . '-' . substr($vStr, 10, 2) . '-' . substr($vStr, 12, 2);
    } else {
        $vBirthday = '19' . substr($vStr, 6, 2) . '-' . substr($vStr, 8, 2) . '-' . substr($vStr, 10, 2);
    }
    if (date('Y-m-d', strtotime($vBirthday)) != $vBirthday) return false;
    if ($vLength == 18) {
        $vSum = 0;
        for ($i = 17; $i >= 0; $i--) {
            $vSubStr = substr($vStr, 17 - $i, 1);
            $vSum += (pow(2, $i) % 11) * (($vSubStr == 'a') ? 10 : intval($vSubStr, 11));
        }
        if ($vSum % 11 != 1) return false;
    }
    return true;
}

/**
 * @param $str
 * @return string
 */
function substr_cut($str)
{
    $strlen     = mb_strlen($str, 'utf-8');
    if (0 < $strlen && $strlen <= 6) {
        $num = 2;
    } else {
        $num = 3;
    }
    $firstStr     = mb_substr($str, 0, $num, 'utf-8');
    $lastStr     = mb_substr($str, -3, $num, 'utf-8');
    return $strlen == 2 ? $firstStr . str_repeat('*', mb_strlen($str, 'utf-8') - 1) : $firstStr . substr(str_repeat("*", $strlen - 2), 0, 6) . $lastStr;
}

/**
 * 作用：用*号替代姓名除第一个字之外的字符
 */
function starReplace($name, $num = 0)
{
    if ($num && mb_strlen($name, 'UTF-8') > $num) {
        return mb_substr($name, 0, 4) . '*';
    }

    if ($num && mb_strlen($name, 'UTF-8') <= $num) {
        return $name;
    }

    $doubleSurname = [
        '欧阳',
        '太史',
        '端木',
        '上官',
        '司马',
        '东方',
        '独孤',
        '南宫',
        '万俟',
        '闻人',
        '夏侯',
        '诸葛',
        '尉迟',
        '公羊',
        '赫连',
        '澹台',
        '皇甫',
        '宗政',
        '濮阳',
        '公冶',
        '太叔',
        '申屠',
        '公孙',
        '慕容',
        '仲孙',
        '钟离',
        '长孙',
        '宇文',
        '司徒',
        '鲜于',
        '司空',
        '闾丘',
        '子车',
        '亓官',
        '司寇',
        '巫马',
        '公西',
        '颛孙',
        '壤驷',
        '公良',
        '漆雕',
        '乐正',
        '宰父',
        '谷梁',
        '拓跋',
        '夹谷',
        '轩辕',
        '令狐',
        '段干',
        '百里',
        '呼延',
        '东郭',
        '南门',
        '羊舌',
        '微生',
        '公户',
        '公玉',
        '公仪',
        '梁丘',
        '公仲',
        '公上',
        '公门',
        '公山',
        '公坚',
        '左丘',
        '公伯',
        '西门',
        '公祖',
        '第五',
        '公乘',
        '贯丘',
        '公皙',
        '南荣',
        '东里',
        '东宫',
        '仲长',
        '子书',
        '子桑',
        '即墨',
        '达奚',
        '褚师',
        '吴铭'
    ];

    $surname = mb_substr($name, 0, 2);
    if (in_array($surname, $doubleSurname)) {
        $name = mb_substr($name, 0, 2) . str_repeat('*', (mb_strlen($name, 'UTF-8') - 2));
    } else {
        $name = mb_substr($name, 0, 1) . str_repeat('*', (mb_strlen($name, 'UTF-8') - 1));
    }

    return $name;
}

/**
 *添加获取游客用户id的方法
 * cookie记录用户的游客id
 **/
function getUseridsssT()
{
    $add['ip'] = ip2long(get_client_ip());
    $kefutally = !empty($_COOKIE['kefutally']) ? $_COOKIE['kefutally'] : '';
    if (empty($kefutally)) {
        $kefutally = md5(microtime() . $add['ip'] . rand());
        setcookie("kefutally", $kefutally, time() + 3600 * 24 * 7, '/');
    }
    return  $kefutally;
}

/**
 * 通用加密
 * @param String $string 需要加密的字串
 * @param String $skey 加密EKY
 * @return String
 */
function midEnCode($string = '', $skey = 'gwbn_kefu')
{
    $skey = array_reverse(str_split($skey));
    $strArr = str_split(base64_encode($string));
    $strCount = count($strArr);
    foreach ($skey as $key => $value) {
        $key < $strCount && $strArr[$key] .= $value;
    }
    return str_replace(array('=', '+', '/'), array('O0O0O', 'o000o', 'oo00o'), join('', $strArr));
}

/**
 * 通用解密
 * @param String $string 需要解密的字串
 * @param String $skey 解密KEY
 * @return String
 */
function midDeCode($string = '', $skey = 'gwbn_kefu')
{
    $skey = array_reverse(str_split($skey));
    $strArr = str_split(str_replace(array('O0O0O', 'o000o', 'oo00o'), array('=', '+', '/'), $string), 2);
    $strCount = count($strArr);
    foreach ($skey as $key => $value) {
        $key < $strCount && $strArr[$key] = $strArr[$key][0];
    }
    return base64_decode(join('', $strArr));
}

/**
 * 防止XXS 过滤函数
 * @param $arr
 */
function xss_clean($html)
{
    $html = addslashes(htmlspecialchars(strip_tags(trim($html))));

    return $html;
}

/**
 * 随机生成n条手机号
 * @param  [type] $n [description]
 * @return [type]    [description]
 */
function randomMobile($n)
{
    $tel_arr = [
        '130',
        '131',
        '132',
        '133',
        '134',
        '135',
        '136',
        '137',
        '138',
        '139',
        '144',
        '147',
        '150',
        '151',
        '152',
        '153',
        '155',
        '156',
        '157',
        '158',
        '159',
        '176',
        '177',
        '178',
        '180',
        '181',
        '182',
        '183',
        '184',
        '185',
        '186',
        '187',
        '188',
        '189',
    ];
    for ($i = 0; $i < $n; $i++) {
        $tmp[] = $tel_arr[array_rand($tel_arr)] . mt_rand(1000, 9999) . mt_rand(1000, 9999);
        // $tmp[] = $tel_arr[array_rand($tel_arr)].'xxxx'.mt_rand(1000,9999);
    }
    return array_unique($tmp);
}

/**
 * 手机号隐藏中间4位
 * @param  [type] $str [description]
 * @return [type]      [description]
 */
function hidePhone($str)
{
    $resstr = substr_replace($str, '****', 3, 4);
    return $resstr;
}

/**
 * 获取完整的HOST
 * @return [type] [description]
 */
function getHost()
{
    $http_type = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https')) ? 'https://' : 'http://';
    $back_url = $http_type . $_SERVER['HTTP_HOST'];
    return $back_url;
}

/**
 * 正则匹配手机号
 * @return [type] [description]
 */
function preg_match_mobile($mobile)
{
    if (!is_numeric($mobile)) {
        return false;
    }
    return preg_match('/^1[3|4|5|6|7|8|9][0-9]\d{8}$/', $mobile);
}
