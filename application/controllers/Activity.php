<?php

/**
 * @file            Activity.php
 * @version         1.0
 * @date            Wed, 06 Jun 2019 14:26:37 GMT
 * @description     This is the controller class for data "2020.01"
 */

class ActivityController extends Yaf_Controller_Abstract
{
	public $template_dir;

	public function init()
	{
		$this->template_dir = Yaf_Registry::get('template_dir');
	}

	/**
	 * 渠道活动
	 */
	public function indexAction()
	{
		// 获取渠道ID
		$channel_id = $this->getRequest()->getParam('channel');

		//记录浏览量
		requestGwbnApi('/jst/channelViews', ['channel_id' => $channel_id]);
		
		//展示模板
		$this->getView()->display($this->template_dir . "/activity/act_" . $channel_id . ".html");
		return false;
	}
}
