<?php

/**
 * @file            Error.php
 * @version         1.0
 * @date            Fri, 27 Oct 2017 11:01:21 GMT
 * @description     This is the controller class for data "error"
 */

class ErrorController extends Yaf_Controller_Abstract
{
   //从2.1开始, errorAction支持直接通过参数获取异常
   public function errorAction($exception)
   {
      /*Yaf has a few different types of errors*/
      switch ($exception->getCode()):
         case YAF_ERR_NOTFOUND_MODULE:
         case YAF_ERR_NOTFOUND_CONTROLLER:
         case YAF_ERR_NOTFOUND_ACTION:
         case YAF_ERR_NOTFOUND_VIEW:
            $this->_view->error = '404 Not Found';
            $this->getView()->display(Yaf_Registry::get('template_dir') . "/error/404.html");
            break;
         default:
            $this->_view->error = $exception->getMessage();
            if (Yaf_Registry::get('site')) {
               $this->getView()->display(Yaf_Registry::get('template_dir') . "/error/error.html");
            }else{
               $this->getView()->display("error/error.html");
            }

      endswitch;

      return false;
   }
}
