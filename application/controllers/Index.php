<?php

/**
 * @file            Index.php
 * @version         1.0
 * @date            Fri, 27 Oct 2017 11:01:21 GMT
 * @description     This is the controller class for data "index"
 */

class IndexController extends Yaf_Controller_Abstract
{
    public $template_dir;

    public function init()
    {
        $this->template_dir = Yaf_Registry::get('template_dir');
        $this->getView()->assign("index", "index");
    }

    /** 
     * 首页
     */
    public function indexAction()
    {
        $data = $this->getHomeData($this->template_dir);
        
        // 获取广告数据
        $adPositions = $this->getAdPositions($this->template_dir);
        $ads = $this->extractAds($data['positions'] ?? [], $adPositions);
        
        // 处理专区产品
        $kdtProducts = $this->processZoneProducts($data['cats'][1031] ?? []);
        $unProducts = $this->processZoneProducts($data['cats'][1032] ?? []);
        $dxProducts = $this->processZoneProducts($data['cats'][1033] ?? []);
        
        // 分配视图变量
        $this->assignViewVariables($ads, $kdtProducts, $unProducts, $dxProducts);
        
        // 显示页面
        $this->getView()->display($this->template_dir . '/index/index.html');
        return false;
    }

    /**
     * 获取广告位置配置
     */
    private function getAdPositions($templateDir)
    {
        $adPositions = [
            'space' => $templateDir == 'jst' ? 10 : null,
            'banner' => $templateDir == 'jst' ? 12 : 11,
            'hot' => $templateDir == 'jst' ? 15 : 16,
            'recommend' => $templateDir == 'jst' ? 13 : 14
        ];
        
        return $adPositions;
    }

    /**
     * 提取广告数据
     */
    private function extractAds(array $positions, array $adPositions)
    {
        $ads = [];
        
        foreach ($adPositions as $key => $positionId) {
            if ($positionId !== null) {
                $ads[$key] = $positions[$positionId]['ads'] ?? [];
            }
        }
        
        return $ads;
    }

    /**
     * 处理专区产品
     */
    private function processZoneProducts(array $category)
    {
        $products = $category['products'] ?? [];
        return [
            'cat' => $category,
            'leftProduct' => $products[0] ?? [],
            'rightProducts' => array_slice($products, 1),
        ];
    }

    /**
     * 分配视图变量
     */
    private function assignViewVariables(array $ads, array $kdtProducts, array $unProducts, array $dxProducts)
    {
		// 广告数据
        $this->getView()->assign('space', $ads['space'] ?? []);
        $this->getView()->assign('banner', $ads['banner'] ?? []);
        $this->getView()->assign('hot', $ads['hot'] ?? []);
        $this->getView()->assign('recommend', $ads['recommend'] ?? []);
        
        // 宽带通专区产品
        $this->getView()->assign('kdtCat', $kdtProducts['cat'] ?? []);
        $this->getView()->assign('leftKdtProduct', $kdtProducts['leftProduct'] ?? []);
        $this->getView()->assign('rightKdtProduct', $kdtProducts['rightProducts'] ?? []);
        
        // 联通专区产品
        $this->getView()->assign('unCat', $unProducts['cat'] ?? []);
        $this->getView()->assign('leftUnProduct', $unProducts['leftProduct'] ?? []);
        $this->getView()->assign('rightUnProduct', $unProducts['rightProducts'] ?? []);

        // 电信专区产品
        $this->getView()->assign('dxCat', $dxProducts['cat'] ?? []);
        $this->getView()->assign('leftDxProduct', $dxProducts['leftProduct'] ?? []);
        $this->getView()->assign('rightDxProduct', $dxProducts['rightProducts'] ?? []);
    }

    /**
     * 获取首页数据
     */
    private function getHomeData($siteCode)
    {
        $params = ['site_code' => $siteCode];
        $requestGwbnApi = requestGwbnApi('/jst/getHomeData', $params);
        return $requestGwbnApi['header']['code'] == 200 ? ($requestGwbnApi['body'] ?? []) : [];
    }
}
