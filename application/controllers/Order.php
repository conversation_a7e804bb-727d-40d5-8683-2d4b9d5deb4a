<?php

/**
 * @file            Index.php
 * @version         1.0
 * @date            Fri, 27 Oct 2017 11:01:21 GMT
 * @description     This is the controller class for data "index"
 */

class OrderController extends Yaf_Controller_Abstract
{
	/** 
	 * 下单页
	 */
	public function orderCreatAction()
	{
		$queryData = $this->getRequest()->getQuery();
		$product_id = trim($queryData['id']) ?? 0;
		if (empty($product_id)) {
			throw new Exception('商品已失效或下架');
		}
		$product_id = base64_decode($product_id);
		//获取省市区数据
		$params = ['site_code' => 'jst'];
		$province = requestGwbnApi('/jst/getProvinceData', $params);

		//获取产品信息
		$products = requestGwbnApi('/jst/getProducts', ['product_id' => $product_id]);
		if ($products['header']['code'] != 200) {
			throw new Exception('商品已失效或下架');
		}
		$this->getView()->assign("province", $province['body']);
		$this->getView()->assign("products", $products['body']);
		$this->getView()->assign("gwbn_api_url", Yaf_Registry::get('config')['gwbn_api_url']);
		$this->getView()->display(Yaf_Registry::get('template_dir') . '/order/order-details.html');
		return false;
	}

	/**
	 * 下单结果页
	 */
	public function orderStatusAction()
	{
		$queryData = $this->getRequest()->getQuery();
		$order_id = trim(base64_decode($queryData['order_id'])) ?? '';
		if (empty($order_id)) {
			throw new Exception('订单ID不能为空');
		}
		//获取订单信息
		$order = requestGwbnApi('/jst/getOrderInfo', ['order_id' => $order_id]);
		if ($order['header']['code'] != 200) {
			throw new Exception('订单信息有误');
		}
		$this->getView()->assign("order", $order['body']);
		$this->getView()->display(Yaf_Registry::get('template_dir') . '/order/order-status.html');
		return false;
	}

	/**
	 * 生成验证码
	 */
	public function captchaAction()
	{
		$captcha = new Captcha();
		if (get_source()['source'] == '2') {
			$captcha->generate(4, 22, 22, false);
		} else {
			$captcha->generate(4);
		}
		return false;
	}

	/**
	 * 查询页
	 */
	public function orderQueryAction()
	{
		$this->getView()->display(Yaf_Registry::get('template_dir') . '/order/order-query.html');
		return false;
	}

	/**
	 * 查询验证
	 */
	public function orderQueryCheckAction()
	{
		if ($this->getRequest()->isPost()) {
			$postData = $this->getRequest()->getPost();
			//手机号
			$consignee_tel = trim($postData['phone']);
			if (empty($consignee_tel)) {
				jsonReturn(['code' => 400, 'msg' => '手机号不能为空', 'data' => '']);
			}
			//正则验证手机号
			if (!preg_match_mobile($consignee_tel)) {
				jsonReturn(['code' => 400, 'msg' => '请输入正确的手机号', 'data' => '']);
			}

			//验证验证码是否正确
			if (trim($postData['verify']) != $_SESSION['captcha']) {
				jsonReturn(['code' => 400, 'msg' => '验证码有误', 'data' => '']);
			}
			//验证通过，将请求转发到订单查询接口
			$url = "/orderQueryDetails?params=" . base64_encode(json_encode(['consignee_tel' => $consignee_tel]));
			jsonReturn(['code' => 200, 'msg' => '成功', 'data' => $url]);
		}
		jsonReturn(['code' => 400, 'msg' => '获取失败', 'data' => '']);
	}

	/**
	 * 查询结果页
	 * 
	 * @throws Exception 参数错误或查询失败时抛出异常
	 * @return bool
	 */
	public function orderQueryDetailsAction()
	{
		try {
			// 获取并验证基础参数
			$queryData = $this->getRequest()->getQuery();
			if (empty($queryData['params'])) {
				throw new Exception('缺少必要的查询参数');
			}

			// 解析并验证参数格式
			$decodedParams = base64_decode($queryData['params']);
			if ($decodedParams === false) {
				throw new Exception('参数格式错误');
			}

			$params = json_decode($decodedParams, true);
			if (json_last_error() !== JSON_ERROR_NONE) {
				throw new Exception('参数解析失败');
			}

			// 验证手机号参数
			$consignee_tel = isset($params['consignee_tel']) ? trim($params['consignee_tel']) : '';
			if (empty($consignee_tel) || !preg_match_mobile($consignee_tel)) {
				throw new Exception('请输入有效的手机号码');
			}

			// 设置查询超时时间
			$timeout = 10; // 10秒超时
			set_time_limit($timeout);

			// 调用订单查询接口
			$orders = requestGwbnApi('/jst/getOrders', ['consignee_tel' => $consignee_tel]);
			if (!isset($orders['header']['code']) || $orders['header']['code'] != 200) {
				throw new Exception('暂未查询到您的订单信息');
			}

			// 确保返回数据中包含必要的订单信息
			if (empty($orders['body'])) {
				throw new Exception('未找到相关订单信息');
			}

			// 渲染视图
			$this->getView()->assign("orders", $orders['body']);
			$this->getView()->display(Yaf_Registry::get('template_dir') . '/order/order-query-details.html');
			return false;

		} catch (Exception $e) {
			throw $e;
		}
	}
	
	/**
	 * 移动端关注我们
	 *
	 * @return void
	 */
    public function layoutQrcodeAction()
    {
        $this->getView()->display(Yaf_Registry::get('template_dir') . '/layout/qrcode.html');
        return false;
    }
}
