<?php
/** 
 * 插件类定义
 * Autoload.php 
 */
class AutoloadPlugin extends Yaf_Plugin_Abstract {

    private $_config;
    private $_file_load;

    //路由开始之前被触发
    public function routerStartup(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response) {  
        $this->_config = Yaf_Application::app()->getConfig();
        $this->_file_load = Yaf_Loader::getInstance();
    }  

    //路由结束之后触发，此时路由一定正确完成, 否则这个事件不会触发  
    public function routerShutdown(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response) {   
        if(isset($this->_config->application->autofunction) && !empty($this->_config->application->autofunction)){
            $autofunction = explode(',',$this->_config->application->autofunction);
            foreach ($autofunction as $v) {
                if(is_dir($this->_config->application->directory.'/'.$v)){
                    $this->getlist($this->_config->application->directory.'/'.$v,'function');
                }else{
                    throw new Exception($this->_config->application->directory.'/'.$v.' directory not found');
                }
            }
        }
        $this->_file_load->setLibraryPath($this->_config->application->directory.'/library',true);

        if (is_null(Yaf_Registry::get('site'))) {
            throw new Exception("网站暂时无法访问");
        }
    }

    //分发循环开始之前被触发
    public function dispatchLoopStartup(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response) {
        //echo "Plugin DispatchLoopStartup called <br/>\n";
    }

    //分发之前触发	如果在一个请求处理过程中, 发生了forward, 则这个事件会被触发多次
    public function preDispatch(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response) {
        //echo "Plugin PreDispatch called <br/>\n";
    }

    //分发结束之后触发，此时动作已经执行结束, 视图也已经渲染完成. 和preDispatch类似, 此事件也可能触发多次
    public function postDispatch(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response) {
        //echo "Plugin postDispatch called <br/>\n";
    }
    
    //分发循环结束之后触发，此时表示所有的业务逻辑都已经运行完成, 但是响应还没有发送
    public function dispatchLoopShutdown(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response) {
        //echo "Plugin DispatchLoopShutdown called <br/>\n";
    }

    public function preResponse(Yaf_Request_Abstract $request, Yaf_Response_Abstract $response) {
        //echo "Plugin PreResponse called <br/>\n";
    }

    private function getlist($dir_str, $type){
        try{
            $handler = opendir($dir_str);
            $this->_file_load->setLibraryPath($dir_str,true);
            while(($filename = readdir($handler)) !== false) 
            {
               
                if($filename != "." && $filename != ".." && count(scandir($dir_str))>2)
                {
                    if(is_dir($dir_str.'/'.$filename))
                    {
                        $this->getlist($dir_str.'/'.$filename,$type);
                    }else{
                        if(is_file($dir_str.'/'.$filename)){
                            switch ($type) {
                                case 'class':
                                    $fname = pathinfo($filename);
                                    $this->_file_load->autoload($fname['filename']);
                                    break;
                            
                                case 'function':
                                    $this->_file_load->import($dir_str.'/'.$filename);
                                    break;
                            }
                        }else{
                            throw new Exception($dir_str.'/'.$filename.' files not found');
                        }
                    }
                }
            }
            closedir($handler);
        }catch(Exception $e){
            throw new Exception($e->getMessage());
        }
    }
}
