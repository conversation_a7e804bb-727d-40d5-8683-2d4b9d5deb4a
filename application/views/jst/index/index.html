<!DOCTYPE html>
<html lang="zh-CN">
<head>
    {{ include file="jst/layout/meta.html" }}
    {{ include file="jst/layout/count.html" }}
</head>
<body>
<div>
    {{ if (isset($space) && $space) }}
        {{ foreach from=$space item=image }}
            <input type="hidden" name="ad_link" value="{{$image.ad_link}}">
            <input type="hidden" name="jump_type" value="{{$image.jump_type}}">
            <a href="javascript:;" class="popularity-url-top">
                <img src="{{$image.ad_img}}" alt="">
            </a>
        {{ /foreach }}
    {{ /if }}
    {{ include file="jst/layout/header.html" }}
    <div class="container d-flex py-2 justify-content-between nav-blue align-items-center">
        <span class="mx-4 text-line fs-6 color-white">首页</span>
        <a href="/orderQuery" class="text-decoration-none"><span class="mx-4 fs-6 color-white">订单查询</span></a>
    </div>
    <div class="carousel-container">
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="3000" data-bs-pause="false">
            <div class="carousel-inner">
                {{ if (isset($banner) && $banner) }}
                    {{ foreach from=$banner item=image }}
                    <div class="carousel-item active">
                        <img src="{{$image.ad_img}}" alt="banner1">
                    </div>
                    {{ /foreach }}
                {{ /if }}
            </div>

            <!-- 右侧浮动内容 -->
            <div class="right-panel py-5">
                <img src="/common/img/jst/brand.png" class="brand-img" alt="品牌整图">
                <div class="input-group mb-2">
                    <input type="text" name="oneClickProcessing_consignee_tel" class="form-control my-3 text-center" maxlength="11" placeholder="请输入手机号"/>
                </div>
                <button class="btn btn-danger mb-3 oneClickProcessing">一键办理</button>
            </div>
        </div>
        <img src="/common/img/jst/brand-b.png" alt="">
    </div>
    <p class="pricing-section pb-2"></p>
    <!--热门专区-->
    <section class="container py-3">
        <p class="title-wrapper d-flex align-items-center mb-4">
            <img src="/common/img/jst/hot.png" alt="热门" height="20" class="hot-img me-2">
            <span class="fw-bold">热门宽带</span>
            <span class="subtitle ms-2 color-gery">快速报装入口</span>
        </p>
        <div class="row">
            {{ if (isset($hot) && $hot) }}
            {{ foreach from=$hot item=image }}
                <div class="col-md-4 popularity-col">
                    <div class="pricing-card">
                        <img src="{{$image.ad_img}}" alt="100M 1年" class="img-fluid">
                        <div class="small-title my-4">{{$image.ad_name}}</div>
                        <div class="btn-group-custom mb-3">
                            <input type="hidden" name="ad_link" value="{{$image.ad_link}}">
                            <input type="hidden" name="jump_type" value="{{$image.jump_type}}">
                            <!-- <a class="kefu-link"><button class="btn-left">立即咨询</button></a> -->
                            <a class="kefu-link"><button class="btn-right">立即咨询</button></a>
                        </div>
                    </div>
                </div>
            {{ /foreach }}
            {{ /if }}
        </div>
    </section>
    <!--联通专区-->
    <section class="pricing-section broadband-section py-3">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <img src="/common/img/jst/lt.png" alt="Logo" class="width-auto" height="60">
                <div class="d-flex align-items-center">
                    <img src="/common/img/jst/phone.png" alt="Phone" class="width-auto mx-1" height="20">
                    <span class="phone-text">010-87997699</span>
                </div>
            </div>
            <div class="broadband-grid py-3">
                {{ if (isset($leftUnProduct) && $leftUnProduct) }}
                <div class="left-card">
                    <img src="{{$leftUnProduct.img_url}}" alt="产品图">
                    <h4>{{$leftUnProduct.product_name}}</h4>
                    <p class="product_title">{{$leftUnProduct.product_title}}</p>
                    <span class="fs-6 ml">到手价</span>
                    <div class="price-line pb-4">
                        <strong>{{$leftUnProduct.shop_price}}元 <span style="color:#007bff;font-size:16px;display: inline-block;margin: 0 10px;">{{$leftUnProduct.time_length}}年</span></strong>
                        <button class="btn go-handle" data-id="{{$leftUnProduct.product_id}}">立即办理</button>
                    </div>
                </div>
                {{ /if }}
                <div class="right-section">
                    {{ if (isset($rightUnProduct) && $rightUnProduct) }}
                    {{ foreach from=$rightUnProduct item=v }}
                    <div class="right-card">
                        <img src="{{$v.img_url}}" alt="产品图">
                        <div class="content mx-4">
                            <h4>{{$v.product_name}}</h4>
                            <p class="product_title">{{$v.product_title}}</p>
                            <span class="fs-6 ml">到手价</span>
                            <div class="price-line">
                                <strong>{{$v.shop_price}}元 <span style="color:#007bff;font-size:16px;display: inline-block;margin: 0 10px;">{{$v.time_length}}年</span></strong>
                                <button class="btn mx-3 go-handle" data-id="{{$v.product_id}}">立即办理</button>
                            </div>
                        </div>
                    </div>
                    {{ /foreach }}
                    {{ /if }}
                </div>
            </div>
        </div>
    </section>
    <!--电信专区-->
    <section class="pricing-section broadband-section py-3 mt-2">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <img src="/common/img/jst/dianxin.png" alt="Logo" class="width-auto" height="60">
                <div class="d-flex align-items-center">
                    <img src="/common/img/jst/phone.png" alt="Phone" class="width-auto mx-1" height="20"">
                    <span class="phone-text">010-10000</span>
                </div>
            </div>
            <div class="broadband-grid py-3">
                {{ if (isset($leftDxProduct) && $leftDxProduct) }}
                <div class="left-card">
                    <img src="{{$leftDxProduct.img_url}}" alt="产品图">
                    <h4>{{$leftDxProduct.product_name}}</h4>
                    <p class="product_title">{{$leftDxProduct.product_title}}</p>
                    <span class="fs-6 ml">到手价</span>
                    <div class="price-line pb-4">
                        <strong>{{$leftDxProduct.shop_price}}元 <span style="color:#007bff;font-size:16px;display: inline-block;margin: 0 10px;">{{$leftDxProduct.time_length}}年</span></strong>
                        <button class="btn go-handle" data-id="{{$leftDxProduct.product_id}}">立即办理</button>
                    </div>
                </div>
                {{ /if }}
                <div class="right-section">
                    {{ if (isset($rightDxProduct) && $rightDxProduct) }}
                    {{ foreach from=$rightDxProduct item=v }}
                    <div class="right-card">
                        <img src="{{$v.img_url}}" alt="产品图">
                        <div class="content mx-4">
                            <h4>{{$v.product_name}}</h4>
                            <p class="product_title">{{$v.product_title}}</p>
                            <span class="fs-6 ml">到手价</span>
                            <div class="price-line">
                                <strong>{{$v.shop_price}}元 <span style="color:#007bff;font-size:16px;display: inline-block;margin: 0 10px;">{{$v.time_length}}年</span></strong>
                                <button class="btn mx-3 go-handle" data-id="{{$v.product_id}}">立即办理</button>
                            </div>
                        </div>
                    </div>
                    {{ /foreach }}
                    {{ /if }}
                </div>
            </div>
        </div>
    </section>
    <!--宽带通专区-->
    <section class="pricing-section broadband-section py-3 mt-2">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <img src="/common/img/jst/kdt.png" alt="Logo" class="width-auto" height="60">
                <div class="d-flex align-items-center">
                    <img src="/common/img/jst/phone.png" alt="Phone" class="width-auto mx-1" height="20">
                    <span class="phone-text">010-87997699</span>
                </div>
            </div>
            <div class="broadband-grid py-3">
                {{ if (isset($leftKdtProduct) && $leftKdtProduct) }}
                <div class="left-card">
                    <img src="{{$leftKdtProduct.img_url}}" alt="产品图">
                    <h4>{{$leftKdtProduct.product_name}}</h4>
                    <p class="product_title">{{$leftKdtProduct.product_title}}</p>
                    <span class="fs-6 ml">到手价</span>
                    <div class="price-line pb-4">
                        <strong>{{$leftKdtProduct.shop_price}}元 <span style="color:#007bff;font-size:16px;display: inline-block;margin: 0 10px;">{{$leftKdtProduct.time_length}}年</span></strong>
                        <button class="btn go-handle" data-id="{{$leftKdtProduct.product_id}}">立即办理</button>
                    </div>
                </div>
                {{ /if }}
                <div class="right-section">
                    {{ if (isset($rightKdtProduct) && $rightKdtProduct) }}
                    {{ foreach from=$rightKdtProduct item=v }}
                    <div class="right-card">
                        <img src="{{$v.img_url}}" alt="产品图">
                        <div class="content mx-4">
                            <h4>{{$v.product_name}}</h4>
                            <p class="product_title">{{$v.product_title}}</p>
                            <span class="fs-6 ml">到手价</span>
                            <div class="price-line">
                                <strong>{{$v.shop_price}}元 <span style="color:#007bff;font-size:16px;">{{$v.time_length}}年</span></strong>
                                <button class="btn mx-3 go-handle" data-id="{{$v.product_id}}">立即办理</button>
                            </div>
                        </div>
                    </div>
                    {{ /foreach }}
                    {{ /if }}
                </div>
            </div>
        </div>
    </section>
    <!--移动专区-->
    <section class="pricing-section broadband-section py-3 mt-2">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <img src="/common/img/jst/yidong.png" alt="Logo" class="width-auto" height="60">
                <div class="d-flex align-items-center">
                    <img src="/common/img/jst/phone.png" alt="Phone" class="width-auto mx-1" height="20">
                    <span class="phone-text">010-10086</span>
                </div>
            </div>
            <img src="/common/img/jst/wait.png" alt="" class="mb-3">
            <p class="color-wait fs-6 text-center">更多专区敬请期待</p>
        </div>
    </section>
    <section class="container py-3">
        <p class="title-wrapper d-flex align-items-center mb-4">
            <img src="/common/img/jst/popularity.png" alt="人气" class="popularity-img me-2">
            <span class="fw-bold">人气推荐</span>
            <span class="subtitle ms-2 color-gery">爆款活动专区</span>
        </p>
        <div class="row">
            {{ if (isset($recommend) && $recommend) }}
            {{ foreach from=$recommend item=image }}
                <div class="col-md-4 popularity-col">
                    <input type="hidden" name="ad_link" value="{{$image.ad_link}}">
                    <input type="hidden" name="jump_type" value="{{$image.jump_type}}">
                    <a href="javascript:;" class="popularity-url">
                        <img src="{{$image.ad_img}}" alt="{{$image.ad_name}}"></a>
                </div>
            {{ /foreach }}
            {{ /if }}
        </div>
    </section>
    <section class="features text-center py-3 bg-light">
        <div class="container">
            <div class="title">
                <h3 class="color-white mt-3">我们的优势</h3>
                <p class="card-line-long card-line-long2 color-white">为什么要选择鲸速通？</p>
            </div>
            <div class="container py-5">
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="custom-card">
                            <div class="custom-icon">
                                <img alt="" src="/common/img/jst/icon-1.png">
                            </div>
                            <div class="custom-title">覆盖广</div>
                            <div class="custom-text">基本能满足北京全域的安装需求</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="custom-card">
                            <div class="custom-icon">
                                <img alt="" src="/common/img/jst/icon-2.png">
                            </div>
                            <div class="custom-title">选择多</div>
                            <div class="custom-text">可根据用户需求，选择办理任意宽带品牌</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="custom-card">
                            <div class="custom-icon">
                                <img alt="" src="/common/img/jst/icon-3.png">
                            </div>
                            <div class="custom-title">响应快</div>
                            <div class="custom-text">三十分钟极速响应，当天可上门安装</div>
                        </div>
                    </div>
                </div>
                <div class="row g-4 mt-3">
                    <div class="col-md-4">
                        <div class="custom-card">
                            <div class="custom-icon">
                                <img alt="" src="/common/img/jst/icon-4.png">
                            </div>
                            <div class="custom-title">安全高</div>
                            <div class="custom-text">随时随地，保障用户的上网安全</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="custom-card">
                            <div class="custom-icon">
                                <img alt="" src="/common/img/jst/icon-5.png">
                            </div>
                            <div class="custom-title">售后强</div>
                            <div class="custom-text">拥有专业的售后服务团队，7*24小时快速响应</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="custom-card">
                            <div class="custom-icon">
                                <img alt="" src="/common/img/jst/icon-6.png">
                            </div>
                            <div class="custom-title">上网快</div>
                            <div class="custom-text">拥有优质的链路资源，上网就是快</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="module-three text-center py-5 bg-light">
        <div class="features-section">
            <div class="container">
                <div class="features-message">
                    <div class="row">
                        <div class="col-md-8"></div>
                        <div class="col-md-4 ps-sm-5 ml-3">
                            <h2 class="fw-bold mb-2 color-white">家庭组网服务</h2>
                            <p class="color-white">
                                鲸速通，拥有多年为各行业提供Wifi全覆盖服务经验，通过多种用户接入方式，结合领先的安全策略和行为管理，利用智能射频优化、热点负载均衡和智能工勘等技术，能为家庭、企业、大型商业综合体、会展场馆等行业，提供安全、高效、可运营的专业的企业级无线覆盖运维服务。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container features-section-mt">
            <div class="title">
                <h3 class="mt-3">企业服务</h3>
                <p class="card-line-long">不仅仅是家庭用户，我们也竭诚为企业提供网络服务</p>
            </div>
            <div class="row text-center section-3 mt-5">
                <div class="col-md-4 mb-4">
                    <div class="card border-reset">
                        <div class="card-header py-4">
                            <h5 class="card-text card-line-short">企业上网</h5>
                        </div>
                        <div class="card-body position-relative">
                            <p class="card-text">沃信通紫金</p>
                            <p class="card-text">沃信通NAT</p>
                            <p class="card-text">楼宇千兆算网</p>
                            <p class="card-text">5G宽带</p>
                            <button class="btn btn-border" data-bs-toggle="modal" data-bs-target="#exampleModalDrpecn">了解更多</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card border-reset">
                        <div class="card-header py-4">
                            <h5 class="card-text card-line-short">企业上云</h5>
                        </div>
                        <div class="card-body position-relative">
                            <p class="card-text">融合云</p>
                            <p class="card-text">阿里云</p>
                            <p class="card-text">华为云</p>
                            <p class="card-text">腾讯云/京东云</p>
                            <button class="btn btn-border" data-bs-toggle="modal" data-bs-target="#exampleModalDrpecn">了解更多</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card border-reset">
                        <div class="card-header py-4">
                            <h5 class="card-text card-line-short">企业IT运维</h5>
                        </div>
                        <div class="card-body position-relative">
                            <p class="card-text">企业IT运维服务</p>
                            <p class="card-text">企业级虚拟组网服务（IP VPN）</p>
                            <p class="card-text">新型组网服务（SDWAN）</p>
                            <p class="card-text">企业出海IT服务</p>
                            <button class="btn btn-border" data-bs-toggle="modal" data-bs-target="#exampleModalDrpecn">了解更多</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="module-five text-center pt-5 bg-light pb-5">
        <div class="container">
            <div class="row text-center section-3 pb-5 mb-5">
                <div class="col-md-6 mb-4">
                </div>
                <div class="col-md-6 mb-4">
                    <div class="">
                        <p class="fs-6">关于我们</p>
                        <h2 class="fw-bold mb-4 mt-3">鲸速科技</h2>
                        <p class="fs-6 text-sm-start">北京鲸速科技有限公司，作为领先的宽带接入服务提供商，致力于为年轻一代打造无与伦比的网络体验。我们的使命是让每个家庭和企业都能享受到极速、稳定、安全的网络连接，让每一次在线体验都充满激情与活力。</p>
                        <p class="fs-6 text-sm-start">选择我们，让您的网络更暖心。</p>
                        <p class="fs-6 text-sm-start">加入我们，开启未来网络生活。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {{ include file="jst/layout/footer.html" }}
</div>
<input type="hidden" class="form-control" name="source">
<!-- 模态窗 -->
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
     data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-content-bg">
            <div class="modal-header ml-5">
                <img src="/common/img/jst/modal-bg.png" alt="">
                <img src="/common/img/jst/close.png" alt="" class="btn-close-icon" data-bs-dismiss="modal"
                     aria-label="Close">
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3 position-relative">
                        <span class="form-icon"><img src="/common/img/jst/phone-icon.png" alt="Phone" style="width: 20px;"></span>
                        <input type="text" class="form-control" placeholder="请输入您的手机号" name="consignee_tel" maxlength="11">
                    </div>
                    <div class="mb-3 position-relative">
                        <span class="form-icon"><img src="/common/img/jst/position-icon.png" alt="User" style="width: 20px;"></span>
                        <input type="text" class="form-control" placeholder="请输入您的地址" name="address">
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn submit-btn w-50" id="js-submit">提交信息</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="exampleModalDrpecn" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
     data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-content-bg">
            <div class="modal-header ml-5">
                <img src="/common/img/jst/modal-bg.png" alt="">
                <img src="/common/img/jst/close.png" alt="" class="btn-close-icon" data-bs-dismiss="modal"
                     aria-label="Close">
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3 position-relative">
                        <span class="form-icon"><img src="/common/img/jst/company_name-icon.png" alt="User" style="width: 20px;"></span>
                        <input type="text" class="form-control" placeholder="请输入您的公司名称" name="company_name">
                    </div>
                    <div class="mb-3 position-relative">
                        <span class="form-icon"><img src="/common/img/jst/remark-icon.png" alt="User" style="width: 20px;"></span>
                        <input type="text" class="form-control" placeholder="请输入您的产品需求" name="remark">
                    </div>
                    <div class="mb-3 position-relative">
                        <span class="form-icon"><img src="/common/img/jst/phone-icon.png" alt="Phone" style="width: 20px;"></span>
                        <input type="text" class="form-control" placeholder="请输入您的手机号" name="consignee_tel2" maxlength="11">
                    </div>
                    <div class="mb-3 position-relative">
                        <span class="form-icon"><img src="/common/img/jst/position-icon.png" alt="User" style="width: 20px;"></span>
                        <input type="text" class="form-control" placeholder="请输入您的地址" name="address2">
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn submit-btn w-50" id="submit-drpecn">提交信息</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</body>
<script type="text/javascript" src="/common/js/index/index.js"></script>
</html>