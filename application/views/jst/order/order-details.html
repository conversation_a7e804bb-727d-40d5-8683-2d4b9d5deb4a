{{ include file="jst/layout/meta.html" }}
{{ include file="jst/layout/count.html" }}
<style>
    html, body {
        height: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
    }

    .order-details-section {
        flex: 1;
    }
    header{
        border-bottom: 1px solid #dee2e6;
    }
    .product-section .container,.order-section .container{
        width: 900px;
    }

    /* 地址选择弹框样式 */
    .address-list-container {
        max-height: 400px;
        overflow-y: auto;
        padding: 0 15px;
    }
    .address-item {
        padding: 12px 15px;
        border-bottom: 1px solid #dee2e6;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border-radius: 4px;
        margin-bottom: 5px;
    }
    .address-item:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }
    .address-item.selected {
        background-color: #e8f0fe;
        border-left: 1px solid #4F85E6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .address-item.selected:hover {
        background-color: #e8f0fe;
    }
    .address-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    .address-item .address-text {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        color: #333;
    }
    .address-item .address-text::before {
        content: '';
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    .address-item.selected .address-text::before {
        background-color: #4F85E6;
        border-color: #4F85E6;
    }

    /* 加载状态样式 */
    .loading {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .bi-hourglass {
        display: none;
        color: #6c757d;
    }
    .loading .bi-hourglass {
        display: inline-block;
        animation: spin 1.5s linear infinite;
        transform-origin: center;
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 模态框样式优化 */
    .modal-content {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .modal-header {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }
    .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #212529;
    }
    .modal-body {
        padding: 15px;
    }
    .modal-footer {
        padding: 15px;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }
    .modal-footer .btn {
        padding: 8px 24px;
        font-size: 14px;
        border-radius: 4px;
    }
</style>
<body>
{{ include file="jst/layout/header.html" }}
<div class="order-details-section">
    <div class="order-section pt-5 pb-1">
        <div class="container">
            <h2 class="section-title">订单信息</h2>
            <div class="row">
                <!-- 姓名 -->
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="name" class="col-1 col-form-label text-end">姓名</label>
                    <div class="col-5">
                        <input type="text" class="form-control" id="name" placeholder="请输入姓名">
                    </div>
                    <label for="phone" class="col-1 col-form-label text-end">电话</label>
                    <div class="col-5">
                        <input type="tel" class="form-control" id="phone" maxlength="11" placeholder="请输入手机号">
                    </div>
                </div>
                <!-- 地址 -->
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="province" class="col-1 col-form-label text-end">地址</label>
                    <div class="col-2">
                        <select class="form-select" id="province">
                            <option value="{{$province.pro.id}}" selected>{{$province.pro.area_name}}</option>
                        </select>
                    </div>
                    <div class="col-2">
                        <select class="form-select" id="city">
                            <option value="{{$province.city.id}}" selected>{{$province.city.area_name}}</option>
                        </select>
                    </div>
                    <div class="col-2">
                        <select class="form-select" id="district">
                            <option value="" selected>请选择</option>
                            {{ foreach from=$province.area item=pro }}
                                <option value="{{$pro.id}}">{{$pro.area_name}}</option>
                            {{ /foreach }}
                        </select>
                    </div>
                    <div class="col-5">
                        <div class="input-group">
                            <input type="text" class="form-control" id="address" placeholder="详细地址,请填至小区楼号门牌号">
                            {{ if ($products[0].cat_id==1033) }}
                            <span class="input-group-text address-search" data-gwbn-url="{{$gwbn_api_url}}">
                                <i class="bi bi-search"></i>
                                <i class="bi bi-hourglass"></i>
                            </span>
                            {{ /if }}
                        </div>
                    </div>
                </div>
                {{ if ($products[0].cat_id==1033) }}
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="certNumber" class="col-1 col-form-label text-end">身份证</label>
                    <div class="col-5">
                        <input type="text" class="form-control" id="certNumber" maxlength="18"  placeholder="请输入身份证">
                    </div>
                </div>
                {{ /if }}
            </div>
        </div>
    </div>
    <!-- Product Selection Section -->
    <div class="product-section my-3">
        <div class="container">
            <div class="product-header fs-5"></div>
            <div class="row row-cols-1 row-cols-md-3 g-4 mt-3">
                <!-- Product 1 -->
                {{ if (isset($products) && $products) }}
                {{ foreach from=$products item=v }}
                <div class="col">
                    <div class="product-card" data-product-id="{{$v.product_id}}" data-product-name="{{$v.product_name}}" data-price="{{$v.shop_price}}" data-cat-id="{{$v.cat_id}}">
                        <h3 class="product-title mt-3">{{$v.product_name}}</h3>
                        <p class="monthly-price">{{$v.product_title}}</p>
                        <p class="total-price">¥{{$v.shop_price}}元 <span class="price-tag">{{$v.time_length}}年</span><span class="bandwidth fs-5">{{$v.kd_rate}}M</span></p>
                    </div>
                </div>
                {{ /foreach }}
                {{ /if }}
            </div>
            <div class="text-center mt-4">
                <button class="submit-btn" id="orderButton" data-gwbn-url="{{$gwbn_api_url}}">立即下单</button>
            </div>
        </div>
    </div>
</div>
{{ include file="jst/layout/footer.html" }}
    <!-- 地址选择弹框 -->
    <div class="modal fade" id="addressModal" tabindex="-1" aria-labelledby="addressModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <h5 class="modal-title m-0" id="addressModalLabel">地址选择</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="address-list-container">
                        <div class="address-list">
                            <!-- 地址列表将通过 JavaScript 动态填充 -->
                        </div>
                        <input type="hidden" id="relation_cooperate_channel">
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="d-flex justify-content-end w-100">
                        <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmAddress">确认</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="text/javascript" src="/common/js/order/order-details.js"></script>
