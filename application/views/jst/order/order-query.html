{{ include file="jst/layout/meta.html" }}
<style>
    html, body {
        height: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
    }

    .order-query-section {
        flex: 1;
    }
    header{
        border-bottom: 1px solid #dee2e6;
    }
    .order-query-section.container{
        width: 900px;
    }
</style>
<body>
{{ include file="jst/layout/header.html" }}
<div class="order-query-section pt-5">
    <div class="container">
        <div class="row justify-content-center align-items-center">
            <div class="col-lg-10">
                <div class="order-container row align-items-center"  style="height: calc(100vh - 220px);">
                    <div class="col-md-6  col-sm-6 order-image text-center">
                    </div>
                    <!-- 右侧订单查询 -->
                    <div class="col-md-6 col-sm-6 order-query">
                        <p class="text-center mb-4 fs-3">订单查询</p>
                        <form action="" method="">
                            <div class="mx-4">
                                <div class="mb-3">
                                    <input type="tel" name="phone" class="form-control input-lg" placeholder="请输入下单手机号" maxlength="11">
                                </div>
                                <div class="mb-3 position-relative">
                                    <input type="text" name="verify" class="form-control pe-5" maxlength="4" placeholder="请输入验证码">
                                    <img class="verify-reset js_reset position-absolute" src="/order/captcha" alt="">
                                </div>
                                <button type="button" class="btn btn-primary w-100 order-query-btn">立即查询</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{ include file="jst/layout/footer.html" }}
</body>
<script type="text/javascript" src="/common/js/order/order-query.js"></script>