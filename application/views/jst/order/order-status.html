{{ include file="jst/layout/meta.html" }}
<style>
    html, body {
        height: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
    }
    header{
        border-bottom: 1px solid #dee2e6;
    }
    .order-status-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center; /* 垂直居中 */
        text-align: center;
    }
    .order-status-section.container {
        width: 900px;
    }
</style>
<body>
{{ include file="jst/layout/header.html" }}
    <div class="order-status-section container">
        <div class="success-header">
            <div class="success-icon">
                <img src="/common/img/jst/checkmark.png" alt="成功" class="p-2">
            </div>
            <h2 class="success-message">订单提交成功!请等待工程师上门安装~</h2>
        </div>
        <div class="total-amount">
            <span class="amount">应付总额：</span>
            <span class="amount-value">{{ $order.order_amount }}元</span>
        </div>
        <div class="order-details mt-5 mx-2">
            <div class="detail-label">订单号：</div>
            <div class="detail-value">{{ $order.order_id }}</div>

            <div class="detail-label">收货地址：</div>
            <div class="detail-value">{{ $order.province_name }} {{ $order.city_name }} {{ $order.area_name }} {{ $order.address }}</div>

            <div class="detail-label">商品名称：</div>
            <div class="detail-value">{{ $order.product_name }} {{ $order.time_length }}年 {{ $order.kd_rate }}M</div>
        </div>
    </div>
   <!-- <div class="order-status-section container" >
        <div class="success-header justify-content-center">
            <div class="success-icon">
                <img src="/common/img/jst/error.png" alt="成功" class="p-2">
            </div>
            <h2 class="success-message">订单提交成功!请等待工程师上门安装~</h2>
        </div>
    </div>-->
{{ include file="jst/layout/footer.html" }}
</body>
<script type="text/javascript" src="/common/js/order/order-details.js"></script>
