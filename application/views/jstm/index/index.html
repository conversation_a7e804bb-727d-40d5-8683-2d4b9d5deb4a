<!DOCTYPE html>
<html lang="zh-CN">

<head>
    {{ include file="jstm/layout/meta.html" }}
    {{ include file="jst/layout/count.html" }}
</head>

<body>
    <div>
        {{ if (isset($space) && $space) }}
        {{ foreach from=$space item=image }}
        <input type="hidden" name="ad_link" value="{{$image.ad_link}}">
        <input type="hidden" name="jump_type" value="{{$image.jump_type}}">
        <a href="javascript:;" class="popularity-url-top">
            <img src="{{$image.ad_img}}" alt="">
        </a>
        {{ /foreach }}
        {{ /if }}
        {{ include file="jstm/layout/header.html" }}
        <div class="carousel-container">
            <div id="bannerCarousel" class="carousel slide mobile-carousel" data-bs-ride="carousel"
                data-bs-interval="3000" data-bs-pause="false">
                <div class="carousel-inner">
                    {{ if (isset($banner) && $banner) }}
                    {{ foreach from=$banner item=image name=bannerLoop }}
                    <div class="carousel-item {{if $smarty.foreach.bannerLoop.first}}active{{/if}}">
                        <img src="{{$image.ad_img}}" alt="banner{{$smarty.foreach.bannerLoop.iteration}}">
                    </div>
                    {{ /foreach }}
                    {{ /if }}
                </div>
            </div>
            <img src="/common/img/jstm/brand-b.png" class="brand-b-img px-3 my-3" alt="">
            <div class="d-flex justify-content-center align-items-center py-3 mx-3">
                <input type="text" name="oneClickProcessing_consignee_tel" class="form-control me-2 text-center"
                    placeholder="请输入手机号" style="max-width: 250px;">
                <button class="btn btn-danger oneClickProcessing">一键办理</button>
            </div>
        </div>
        <p class="pricing-section pb-2"></p>
        <section class="container py-3">
            <p class="title-wrapper d-flex align-items-center mb-4">
                <img src="/common/img/jstm/hot.png" alt="热门" height="20" class="hot-img me-2 mx-1">
                <span class="fw-bold">热门宽带</span>
                <span class="subtitle ms-2 color-gery">快速报装入口</span>
            </p>
            <div class="d-flex justify-content-center">
                {{ if (isset($hot) && $hot) }}
                {{ foreach from=$hot item=image }}
                <div class="popularity-col px-2 py-2">
                    <div class="pricing-card">
                        <img src="{{$image.ad_img}}" alt="100M 1年" class="img-fluid">
                        <div class="small-title my-2">{{$image.ad_name}}</div>
                        <div class="btn-group-custom">
                            <input type="hidden" name="ad_link" value="{{$image.ad_link}}">
                            <input type="hidden" name="jump_type" value="{{$image.jump_type}}">
                            <!-- <a class="kefu-link me-1"><button class="btn-left">立即咨询</button></a> -->
                            <a class="kefu-link me-1"><button class="btn-right">立即咨询</button></a>
                        </div>
                    </div>
                </div>
                {{ /foreach }}
                {{ /if }}
            </div>
        </section>
        <!--联通专区-->
        <section class="pricing-section py-3">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <img src="/common/img/jstm/lt_m.png" alt="Logo" class="width-auto brand-logo" height="35">
                    <div class="d-flex align-items-center">
                        <a href="tel:010-87997699" class="tel-link">
                            <img src="/common/img/jstm/phone.png" alt="Phone" class="width-auto mx-1" height="20">
                            <span class="phone-text">010-87997699</span>
                        </a>
                    </div>
                </div>
                <div class="broadband-packages">
                    {{ if (isset($leftUnProduct) && $leftUnProduct) }}
                    <div class="left-package">
                        <div>
                            <img src="{{$leftUnProduct.img_url}}" alt="产品图">
                        </div>
                        <!-- <div class="border-div mx-1 mt-2"></div> -->
                        <div class="package-name fs-7 mx-2 mt-1">{{$leftUnProduct.product_name}}</div>
                        <div class="desc-text mx-2 my-1">{{$leftUnProduct.product_title}}</div>
                        <div class="price-label fs-7 mx-2">到手价</div>
                        <div class="d-flex justify-content-between mx-2 mb-2">
                            <div class="price-row">
                                <span class="price-number">{{$leftUnProduct.shop_price}}元</span>
                                <span class="year-term">{{$leftUnProduct.time_length}}年</span>
                            </div>
                            <button class="action-btn go-handle px-2" data-id="{{$leftUnProduct.product_id}}">立即办理</button>
                        </div>
                    </div>
                    {{ /if }}
                    <div class="right-packages">
                        {{ if (isset($rightUnProduct) && $rightUnProduct) }}
                        {{ foreach from=$rightUnProduct item=v }}
                        <div class="top-package">
                            <div class="d-flex">
                                 <div class="top-package-img" style="background-image: url('{{$v.m_img_url}}');"></div>
                                <div class="pb-2 m-sm-l">
                                    <div class="package-name fs-7">{{$v.product_name}}</div>
                                    <div class="desc-text">{{$v.product_title}}</div>
                                    <div class="">
                                        <span class="rice-label fs-7 pt-2">到手价</span>
                                        <span class="price-number">{{$v.shop_price}}元</span>
                                        <span class="year-term fs-7">{{$v.time_length}}年</span>
                                    </div>
                                    <button class="action-btn go-handle px-2" data-id="{{$v.product_id}}">立即办理</button>
                                </div>
                            </div>
                        </div>
                        {{ /foreach }}
                        {{ /if }}
                    </div>
                </div>
            </div>
        </section>
        <!--电信专区-->
        <section class="pricing-section py-3 mt-2">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <img src="/common/img/jstm/dx_m.png" alt="Logo" class="width-auto brand-logo" height="35">
                    <div class="d-flex align-items-center">
                        <a href="tel:010-10000" class="tel-link">
                            <img src="/common/img/jst/phone.png" alt="Phone" class="width-auto mx-1" height="20"">
                            <span class=" phone-text">010-10000</span>
                        </a>
                    </div>
                </div>
                <div class="broadband-packages">
                    {{ if (isset($leftDxProduct) && $leftDxProduct) }}
                    <div class="left-package">
                        <div>
                            <img src="{{$leftDxProduct.img_url}}" alt="产品图">
                        </div>
                        <!-- <div class="border-div mx-1 mt-2"></div> -->
                        <div class="package-name fs-7 mx-2 mt-1">{{$leftDxProduct.product_name}}</div>
                        <div class="desc-text mx-2 my-1">{{$leftDxProduct.product_title}}</div>
                        <div class="price-label fs-7 mx-2">到手价</div>
                        <div class="d-flex justify-content-between mx-2 mb-2">
                            <div class="price-row">
                                <span class="price-number">{{$leftDxProduct.shop_price}}元</span>
                                <span class="year-term">{{$leftDxProduct.time_length}}年</span>
                            </div>
                            <button class="action-btn go-handle px-2" data-id="{{$leftDxProduct.product_id}}">立即办理</button>
                        </div>
                    </div>
                    {{ /if }}
                    <div class="right-packages">
                        {{ if (isset($rightDxProduct) && $rightDxProduct) }}
                        {{ foreach from=$rightDxProduct item=v }}
                        <div class="top-package">
                            <div class="d-flex">
                                <div class="top-package-img" style="background-image: url('{{$v.m_img_url}}');"></div>
                                <div class="pb-2 m-sm-l">
                                    <div class="package-name fs-7">{{$v.product_name}}</div>
                                    <div class="desc-text">{{$v.product_title}}</div>
                                    <div class="">
                                        <span class="rice-label fs-7 pt-2">到手价</span>
                                        <span class="price-number">{{$v.shop_price}}元</span>
                                        <span class="year-term fs-7">{{$v.time_length}}年</span>
                                    </div>
                                    <button class="action-btn go-handle px-2" data-id="{{$v.product_id}}">立即办理</button>
                                </div>
                            </div>
                        </div>
                        {{ /foreach }}
                        {{ /if }}
                    </div>
                </div>
            </div>
        </section>
        <!--宽带通专区-->
        <section class="pricing-section broadband-section py-3 mt-2">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <img src="/common/img/jstm/kdt_m.png" alt="Logo" class="width-auto brand-logo" height="35">
                    <div class="d-flex align-items-center">
                        <a href="tel:010-87997699" class="tel-link">
                            <img src="/common/img/jstm/phone.png" alt="Phone" class="width-auto mx-1" height="20">
                            <span class="phone-text">010-87997699</span>
                        </a>
                    </div>
                </div>
                <div class="broadband-packages">
                    {{ if (isset($leftKdtProduct) && $leftKdtProduct) }}
                    <div class="left-package">
                        <div>
                            <img src="{{$leftKdtProduct.img_url}}" alt="产品图">
                        </div>
                        <!-- <div class="border-div mx-1 mt-2"></div> -->
                        <div class="package-name fs-7 mx-2 mt-1">{{$leftKdtProduct.product_name}}</div>
                        <div class="desc-text mx-2 my-1">{{$leftKdtProduct.product_title}}</div>
                        <div class="price-label fs-7 mx-2">到手价</div>
                        <div class="d-flex justify-content-between mx-2 mb-2">
                            <div class="price-row">
                                <span class="price-number">{{$leftKdtProduct.shop_price}}元</span>
                                <span class="year-term">{{$leftKdtProduct.time_length}}年</span>
                            </div>
                            <button class="action-btn go-handle px-2" data-id="{{$leftKdtProduct.product_id}}">立即办理</button>
                        </div>
                    </div>
                    {{ /if }}
                    <div class="right-packages">
                        {{ if (isset($rightKdtProduct) && $rightKdtProduct) }}
                        {{ foreach from=$rightKdtProduct item=v }}
                        <div class="top-package">
                            <div class="d-flex">
                                <div class="top-package-img" style="background-image: url('{{$v.m_img_url}}');"></div>
                                <div class="pb-2 m-sm-l">
                                    <div class="package-name fs-7">{{$v.product_name}}</div>
                                    <div class="desc-text">{{$v.product_title}}</div>
                                    <div class="">
                                        <span class="rice-label fs-7 pt-2">到手价</span>
                                        <span class="price-number">{{$v.shop_price}}元</span>
                                        <span class="year-term fs-7">{{$v.time_length}}年</span>
                                    </div>
                                    <button class="action-btn go-handle px-2" data-id="{{$v.product_id}}">立即办理</button>
                                </div>
                            </div>
                        </div>
                        {{ /foreach }}
                        {{ /if }}
                    </div>
                </div>
            </div>
        </section>
        <!--移动专区-->
        <section class="pricing-section broadband-section pt-3 mt-2">
            <div class="container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <img src="/common/img/jstm/yd_m.png" alt="Logo" class="width-auto brand-logo" height="35">
                    <div class="d-flex align-items-center">
                        <a href="tel:010-10086" class="tel-link">
                            <img src="/common/img/jst/phone.png" alt="Phone" class="width-auto mx-1" height="20">
                            <span class="phone-text">010-10086</span>
                        </a>
                    </div>
                </div>
                <img src="/common/img/jst/wait.png" alt="" class="mb-3">
                <p class="color-wait fs-7 text-center pb-3">更多专区敬请期待</p>
            </div>
        </section>
        <section class="container pt-3">
            <p class="title-wrapper d-flex align-items-center mb-4">
                <img src="/common/img/jst/popularity.png" alt="人气" class="popularity-img me-2">
                <span class="fw-bold">人气推荐</span>
                <span class="subtitle ms-2 color-gery">爆款活动专区</span>
            </p>
            <div class="d-flex justify-content-between align-items-center mb-4">
                {{ if (isset($recommend) && $recommend) }}
                {{ foreach from=$recommend item=image }}
                <div class="col-md-4 popularity-col">
                    <input type="hidden" name="ad_link" value="{{$image.ad_link}}">
                    <input type="hidden" name="jump_type" value="{{$image.jump_type}}">
                    <a href="javascript:;" class="popularity-url">
                        <img src="{{$image.ad_img}}" alt="{{$image.ad_name}}"></a>
                </div>
                {{ /foreach }}
                {{ /if }}
            </div>
        </section>
        <section class="container pt-3">
            <img src="/common/img/jstm/our_advantages.png" alt="">
        </section>
        <section class="mt-4">
            <img src="/common/img/jstm/family.png" alt="">
        </section>
        <section class="enterprise-services py-2">
            <div class="container my-4">
                <div class="title text-center">
                    <h5 class="mt-3">企业服务</h5>
                    <p class="fs-6 fs-6-color">不仅仅是家庭用户，我们也竭诚为企业提供网络服务</p>
                </div>
                <div class="" data-bs-toggle="modal" data-bs-target="#exampleModalDrpecn">
                    <img src="/common/img/jstm/enterprise-services-bg.png" alt="" class="img-fluid">
                </div>
            </div>
        </section>
        <section class="mt-4">
            <img src="/common/img/jstm/about-us.png" alt="">
        </section>
        {{ include file="jstm/layout/footer.html" }}
    </div>
    <input type="hidden" class="form-control" name="source">
    <!-- 模态窗 -->
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"
        data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-content-bg mx-4">
                <div class="modal-header ml-5">
                    <img src="/common/img/jst/modal-bg.png" alt="">
                    <img src="/common/img/jst/close.png" alt="" class="btn-close-icon" data-bs-dismiss="modal"
                        aria-label="Close">
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3 position-relative">
                            <span class="form-icon"><img src="/common/img/jst/phone-icon.png" alt="Phone"
                                    style="width: 20px;"></span>
                            <input type="text" class="form-control" placeholder="请输入您的手机号" name="consignee_tel"
                                maxlength="11">
                        </div>
                        <div class="mb-3 position-relative">
                            <span class="form-icon"><img src="/common/img/jst/position-icon.png" alt="User"
                                    style="width: 20px;"></span>
                            <input type="text" class="form-control" placeholder="请输入您的地址" name="address">
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn submit-btn w-50" id="js-submit">提交信息</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="exampleModalDrpecn" tabindex="-1" aria-labelledby="exampleModalLabel2"
        aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-content-bg mx-4">
                <div class="modal-header ml-5">
                    <img src="/common/img/jst/modal-bg.png" alt="">
                    <img src="/common/img/jst/close.png" alt="" class="btn-close-icon" data-bs-dismiss="modal"
                        aria-label="Close">
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3 position-relative">
                            <span class="form-icon"><img src="/common/img/jst/company_name-icon.png" alt="User"
                                    style="width: 20px;"></span>
                            <input type="text" class="form-control" placeholder="请输入您的公司名称" name="company_name">
                        </div>
                        <div class="mb-3 position-relative">
                            <span class="form-icon"><img src="/common/img/jst/remark-icon.png" alt="User"
                                    style="width: 20px;"></span>
                            <input type="text" class="form-control" placeholder="请输入您的产品需求" name="remark">
                        </div>
                        <div class="mb-3 position-relative">
                            <span class="form-icon"><img src="/common/img/jst/phone-icon.png" alt="Phone"
                                    style="width: 20px;"></span>
                            <input type="text" class="form-control" placeholder="请输入您的手机号" name="consignee_tel2"
                                maxlength="11">
                        </div>
                        <div class="mb-3 position-relative">
                            <span class="form-icon"><img src="/common/img/jst/position-icon.png" alt="User"
                                    style="width: 20px;"></span>
                            <input type="text" class="form-control" placeholder="请输入您的地址" name="address2">
                        </div>
                        <div class="text-center">
                            <button type="button" class="btn submit-btn w-50" id="submit-drpecn">提交信息</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="text/javascript" src="/common/js/index/index.js"></script>

</html>