{{ include file="jstm/layout/meta.html" }}
{{ include file="jst/layout/count.html" }}
<style>
    html, body {
        height: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
    }

    .order-details-section {
        flex: 1;
    }
    header{
        border-bottom: 1px solid #dee2e6;
    }

    /* 地址选择弹框样式 */
    .address-list-container {
        max-height: 400px;
        overflow-y: auto;
        padding: 0 15px;
    }
    .address-item {
        padding: 16px 15px;
        border-bottom: 1px solid #dee2e6;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        border-radius: 4px;
        margin-bottom: 8px;
        min-height: 48px;
        -webkit-tap-highlight-color: transparent;
        user-select: none;
    }
    .address-item:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }
    .address-item.selected {
        background-color: #e8f0fe;
        border-left: 1px solid #4F85E6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .address-item.selected:hover {
        background-color: #e8f0fe;
    }
    .address-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    .address-item .address-text {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 16px;
        color: #333;
        width: 100%;
        min-height: 24px;
    }
    .address-item .address-text::before {
        content: '';
        width: 24px;
        height: 24px;
        min-width: 24px;
        min-height: 24px;
        border-radius: 50%;
        border: 2px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        background: #fff;
        box-sizing: border-box;
    }
    .address-item.selected .address-text::before {
        background-color: #4F85E6;
        border-color: #4F85E6;
        box-shadow: 0 0 0 4px rgba(79,133,230,0.15);
    }

    /* 加载状态样式 */
    .loading {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .bi-hourglass {
        display: none;
        color: #6c757d;
    }
    .loading .bi-hourglass {
        display: inline-block;
        animation: spin 1.5s linear infinite;
        transform-origin: center;
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 模态框样式优化 */
    .modal-content {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .modal-header {
        padding: 15px;
        border-bottom: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }
    .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #212529;
    }
    .modal-body {
        padding: 15px;
    }
    .modal-footer {
        padding: 15px;
        border-top: 1px solid #dee2e6;
        background-color: #f8f9fa;
    }
    .modal-footer .btn {
        padding: 8px 24px;
        font-size: 14px;
        border-radius: 4px;
    }
</style>
<body>
{{ include file="jstm/layout/header.html" }}
<div class="order-details-section mx-3">
    <div class="order-section pt-5 pb-1">
        <div class="container">
            <h2 class="section-title">订单信息</h2>
            <div class="row">
                <!-- 姓名 -->
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="name" class="col-2 col-form-label text-end">姓名</label>
                    <div class="col-10">
                        <input type="text" class="form-control" id="name" placeholder="请输入姓名">
                    </div>
                </div>
                <!-- 电话 -->
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="phone" class="col-2 col-form-label text-end">电话</label>
                    <div class="col-10">
                        <input type="tel" class="form-control" id="phone" maxlength="11" placeholder="请输入手机号">
                    </div>
                </div>
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="province" class="col-2 col-form-label text-end">地址</label>
                    <div class="col-10 d-flex gap-2">
                        <input type="text" class="form-control col" value="{{$province.pro.area_name}}" readonly>
                        <input type="hidden" id="province" value="{{$province.pro.id}}" readonly>
                        <input type="text" class="form-control col" value="{{$province.city.area_name}}" readonly>
                        <input type="hidden" id="city" value="{{$province.city.id}}" readonly>
                        <select class="form-select col" id="district">
                            <option value="" selected>请选择</option>
                            {{ foreach from=$province.area item=pro }}
                            <option value="{{$pro.id}}">{{$pro.area_name}}</option>
                            {{ /foreach }}
                        </select>
                    </div>
                </div>

                <!-- 详细地址 -->
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="address" class="col-2 col-form-label text-end"></label>
                    <div class="col-10">
                        <div class="input-group">
                            <input type="text" class="form-control" id="address" placeholder="详细地址,请填至小区楼号门牌号">
                            {{ if ($products[0].cat_id==1033) }}
                            <span class="input-group-text address-search" data-gwbn-url="{{$gwbn_api_url}}">
                                <i class="bi bi-search"></i>
                                <i class="bi bi-hourglass" style="vertical-align: middle;"></i>
                            </span>
                            {{ /if }}
                        </div>
                    </div>
                </div>
                {{ if ($products[0].cat_id==1033) }}
                <div class="col-12 mb-3 row d-flex align-items-center">
                    <label for="certNumber" class="col-2 col-form-label text-end">身份证</label>
                    <div class="col-10">
                        <input type="text" class="form-control" id="certNumber" maxlength="18" placeholder="请输入身份证">
                    </div>
                </div>
                {{ /if }}
            </div>
        </div>
    </div>
    <div class="product-section my-3">
        <div class="container">
            <div class="product-header fs-6 mx-5"></div>
            <div class="mx-2 d-flex justify-content-between align-items-center">
                {{ if (isset($products) && $products) }}
                {{ foreach from=$products item=v }}
                <div class="product-card-container mt-4">
                    <div class="product-card px-2" data-product-id="{{$v.product_id}}" data-product-name="{{$v.product_name}}" data-price="{{$v.shop_price}}" data-cat-id="{{$v.cat_id}}">
                        <h3 class="product-title mt-3">{{$v.product_name}}</h3>
                        <p class="monthly-price">{{$v.product_title}}</p>
                        <p class="total-price d-flex justify-content-between align-items-center">
                            <span>¥{{$v.shop_price}}元 </span>
                            <span class="price-tag">{{$v.time_length}}年</span>
                        </p>
                    </div>
                </div>
                {{ /foreach }}
                {{ /if }}
            </div>
            <div class="text-center mt-1">
                <button class="submit-btn" id="orderButton_m" data-gwbn-url="{{$gwbn_api_url}}">立即下单</button>
            </div>
        </div>
    </div>
</div>

{{ include file="jstm/layout/footer.html" }}
<!-- 地址选择弹框 -->
<div class="modal fade" id="addressModal" tabindex="-1" aria-labelledby="addressModalLabel" aria-hidden="true" style="top:11%">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <h5 class="modal-title m-0" id="addressModalLabel">地址选择</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="address-list-container">
                    <div class="address-list">
                        <!-- 地址列表将通过 JavaScript 动态填充 -->
                    </div>
                    <input type="hidden" id="relation_cooperate_channel">
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-end w-100">
                    <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmAddress">确认</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="/common/plugins/weui/js/weui.min.js"></script>
<script>
    $(document).ready(function() {
        // 先将所有卡片高度设为'auto'，避免旧高度影响
        $('.product-card').css('height', 'auto');
        // 计算所有卡片的最大高度
        var maxHeight = 0;
        $('.product-card').each(function() {
            var h = $(this).outerHeight();
            if (h > maxHeight) maxHeight = h;
        });
        // 统一设置为最大高度
        $('.product-card').css('height', maxHeight + 'px');
        // 获取原始 select 里的数据
        var districts = [];
        $("#district option").each(function() {
            districts.push({
                label: $(this).text(),
                value: $(this).val()
            });
        });
        // 当点击地址栏时，触发 WeUI Picker
        $("#district").click(function(event) {
            event.preventDefault();  // 阻止原生的 select 动作
            // 触发 WeUI Picker
            weui.picker(districts, {
                onConfirm: function(result) {
                    // 更新 input 和 select 的值
                    $("#district").val(result[0].value);
                    $("#district option[value='" + result[0].value + "']").prop("selected", true);
                },
                // 可选：自定义 Picker 配置项
                title: '请选择'
            });
        });
    });
</script>
<script type="text/javascript" src="/common/js/order/order-details.js"></script>