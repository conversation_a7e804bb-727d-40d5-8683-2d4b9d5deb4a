{{ include file="jstm/layout/meta.html" }}
<style>
    html, body {
        height: 100%;
        margin: 0;
        display: flex;
        flex-direction: column;
    }
    .order-query-details {
        flex: 1;
        background-size: cover;
        display: flex;
        justify-content: center;
    }
    header{
        border-bottom: 1px solid #dee2e6;
    }
</style>
<body>
{{ include file="jstm/layout/header.html" }}
<div class="order-query-details">
    <div class="order-detail-container">
        {{ if (isset($orders) && $orders) }}
        <div class="order-tabs">
            <div class="order-tab-container">
                {{ foreach from=$orders item=v key=k }}
                <div class="order-tab {{if $k eq 0}}active{{/if}}" data-order="{{$k+1}}">订单{{$k+1}}</div>
                {{ /foreach }}
            </div>
        </div>
        <div class="order-contents">
            {{ foreach from=$orders item=v key=k }}
            <!-- 订单内容 -->
            <div class="order-content" data-order="{{$k+1}}" {{if $k neq 0}}style="display: none;"{{/if}}>
                <div class="order-title">业务受理信息</div>
                <div class="order-info">
                    <div class="info-section">
                        <div class="basic-info">基本信息</div>
                        <div class="order-number">订单号：{{$v.order_id}}</div>
                    </div>
                    <table class="info-table">
                        <tr>
                            <td>姓名</td>
                            <td>{{$v.consignee}}</td>
                        </tr>
                        <tr>
                            <td>联系电话</td>
                            <td>{{$v.consignee_tel}}</td>
                        </tr>
                        <tr>
                            <td>地址详情</td>
                            <td>{{$v.province_name}}{{$v.city_name}}{{$v.area_name}}{{$v.address}}</td>
                        </tr>
                        <tr>
                            <td>套餐名称</td>
                            <td>{{$v.product_name}}</td>
                        </tr>
                        <tr>
                            <td>套餐品牌</td>
                            <td>{{$v.order_type}}</td>
                        </tr>
                        <tr>
                            <td>时长</td>
                            <td>{{$v.time_length}}年</td>
                        </tr>
                        <tr>
                            <td>速率</td>
                            <td>{{$v.kd_rate}}M</td>
                        </tr>
                        <tr>
                            <td>订单状态</td>
                            <td>{{$v.order_status}}</td>
                        </tr>
                        <tr>
                            <td>下单时间</td>
                            <td>{{$v.created_at}}</td>
                        </tr>
                    </table>
                </div>
                <div class="order-total">合计：<span class="price fw-medium">{{$v.order_amount}}元</span></div>
            </div>
            {{ /foreach }}
        </div>
        {{ /if }}
    </div>
</div>
{{ include file="jstm/layout/footer.html" }}
<script>
    // 订单切换功能
    $(document).ready(function() {
        $('.order-tab').click(function() {
            // 移除所有tab的active类
            $('.order-tab').removeClass('active');
            // 为当前点击的tab添加active类
            $(this).addClass('active');
            // 获取当前选中的订单编号
            var orderNum = $(this).data('order');
            // 隐藏所有订单内容
            $('.order-content').hide();
            // 显示对应订单的内容
            $('.order-content[data-order="' + orderNum + '"]').show();
        });
    });
</script>
</body>