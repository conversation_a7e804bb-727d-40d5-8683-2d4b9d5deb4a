[common]
;app
application.debug = TRUE
application.dispatcher.catchException = TRUE
application.dispatcher.throwException = TRUE

application.directory       = APP_PATH "/application"
application.autofunction    = "common"
application.modules         = index


[product : common]
;database
database.config.type        = 'pdo'
database.config.host        = 'localhost'
database.config.name        = 'xp_gwbn'
database.config.user        = 'root' 
database.config.pwd         = '123456'
database.config.port        = '3306'
database.config.charset     = 'utf8'
database.config.prefix      = 'xp_'

database.config.deploy_type = 0 ;数据库部署方式: 0单一服务器, 1主从服务器
database.config.rw_separate = false ;主从式
database.config.master_num  = 1 ;读写分离后 主服务器数量
;指定从服务器序号
database.config.slave_no    = ''
database.config.dsn         = ''
database.config.params      = '' 
database.config.bind_param  = false  
database.config.like_fields = ''


;redis
redis.config.server       = "127.0.0.1"
redis.config.auth         = ""
redis.config.port         = "6379"
redis.config.cluster.type = FALSE
redis.config.cluster.map  = ""


;configures for smarty
smarty.left_delimiter   = "{{"
smarty.right_delimiter  = "}}"
smarty.template_dir     = APP_PATH "/application/views/"
smarty.compile_dir      = APP_PATH "/runtime/cache/smarty/templates_c/"
smarty.cache_dir        = APP_PATH "/runtime/cache/smarty/templates_d/"
application.view.ext    = "html"


;twig
;application.view.ext = twig
;twig.cache           = APP_PATH "/runtime/cache/twig/cache"
;twig.autoescape      = TRUE
;twig.debug           = TRUE

;gwbn api配置
gwbn_api_url            = "http://www.drpapi.com"
gwbn_api_appid          = "xpl_jst_api"
gwbn_api_key            = "2nMtwOPSNYBLGE0mvVk2w_j8kBwclBl1"

;titan tool api配置
titan_tool_api_config.host = 'https://i.96090090.com/tool/'
titan_tool_api_config.appid = 'titan_tool'
titan_tool_api_config.key = 'wyRLVvFyRNYzvBbasdhCNjwqgDA8wJ73XNchKV6jq4grzv9Lmf'


[devel : product]
;twig
;twig.debug = true