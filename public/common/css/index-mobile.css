:root {
    --primary-color: #005DF3;
    --secondary-color: #EFF6FD;
    --text-color: #ffffff;
    --font-family: Arial, sans-serif;
}

img {
    width: 100%;
}

.container {
    /*font-size: 1rem;*/
}
.header-logo{
    width: 8rem;
    height: 1.8rem;
}
.top-bar {
    background-color: #fff;
    color: #005DF3;
    text-shadow: none !important;
}

.top-bar span {
    color: #005DF3;
}

.top-bar .d-flex {
    flex-wrap: nowrap;
}

.top-bar a {
    text-decoration: none;
    white-space: nowrap;
}

.nav-blue {
    background: #005DF3;
    cursor: pointer;
}

.nav-blue .text-line {
    border-bottom: 2px solid #fff;
}

.carousel-container {
    margin: auto;
    position: relative;
    overflow: hidden;
}

.mobile-carousel {
    position: relative;
    width: 100%;
}

.carousel-item img {
    width: 100%;
    height: 11rem;
    display: block;
}

.mobile-panel {
    padding-top: 15px;
    padding-bottom: 15px;
}

.right-panel img.brand-img {
    width: 100%;
    border-radius: 12px;
    margin-bottom: 15px;
}

.brand-b-img {
    width: 100%;
    display: block;
    margin-top: 10px;
}

.right-panel .mobile-input {
    text-align: center;
}

.operator-logos {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
}

.operator-logos img {
    height: 28px;
}

.operator-labels {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

.input-group input {
    border-radius: 50px;
    height: 36px;
    padding-left: 15px;
    box-shadow: none;
    font-size: 14px;
}

.btn-danger {
    border-radius: 5px;
    width: 50%;
    height: 36px;
    font-size: 16px;
    line-height: 18px;
    display: flex;
    margin: auto;
    justify-content: center;
    align-items: center;
    padding: 8px 0;
    background: #F33A3C;
}

.pricing-section {
    background: var(--secondary-color);
}

.tel-link {
    text-decoration: none;
}

.mt-6 {
    margin-top: 5.1rem !important;
}

.color-white {
    color: #ffffff !important;
}

.color-gery {
    color: #505050;
}

.color-blue {
    color: #0247D6;
}

.color-gery::before {
    content: "";
    display: inline-block;
    width: 1px;
    height: 20px;
    background: #505050;
    margin-right: 8px;
    vertical-align: middle;
}

.color-wait {
    color: #6E6E6E;
}

.fs-7 {
    font-size: .7rem;
    font-weight: bold;
}
.fs-8{
    font-size:.5rem;
}
.m-sm-l {
    margin-left: .25rem;
}
.hot-img {
    width: 23px;
    height: 26px;
    vertical-align: middle;
    margin-top: -2px; /* 视觉微调 */
}

.popularity-img {
    width: 26px;
    height: 22px;
    vertical-align: middle;
    margin-top: -2px; /* 视觉微调 */
}

.title-wrapper {
    font-size: 1.25rem; /* fs-5 对应的20px */
    line-height: 1; /* 重置行高 */
}

/* 副标题样式 */
.subtitle {
    font-size: 1rem; /* fs-6 对应的16px */
    font-weight: normal;
    position: relative;
    padding-left: 12px; /* 分隔线位置 */
    display: inline-flex;
    align-items: center;
}

/* 分隔线（替换伪元素方案） */
.subtitle::before {
    content: "";
    position: absolute;
    left: 0;
    height: 20px;
    width: 1px;
    background: #505050;
    transform: translateY(-1px); /* 微调垂直居中 */
}

.popularity-col {
    background-color: #EFF6FD;
}

.popularity-col:nth-child(2) {
    margin: 0 .5rem;
}

.pricing-card {
    background-color: #EFF6FD;
    text-align: center;
}

.package-description {
    padding: 10px;
    font-size: 14px;
    color: #333;
    background-color: #f7f7f7;
}

.action-buttons {
    display: flex;
    padding: 10px;
}

.btn-consult, .btn-process {
    flex: 1;
    border: none;
    padding: 6px 0;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
}

.btn-consult {
    background-color: #fff;
    color: #005DF3;
    border: 1px solid #005DF3;
    margin-right: 5px;
}

.btn-process {
    background-color: #005DF3;
    color: #fff;
    margin-left: 5px;
}

.small-title {
    font-size: .6rem;
    color: #333;
}

.btn-group-custom {
    display: flex;
    justify-content: center; /* 按钮居中 */
    align-items: center;
}

.btn-left, .btn-right {
    width: 3.1rem;
    height: 1.5rem;
    border: none;
    font-size: .5rem;
    cursor: pointer;
    border-radius: 1.5rem;
}

.btn-left {
    background-color: #fff;
    color: #005DF3;
}

.btn-right {
    background-color: #005DF3; /* 蓝色 */
    color: white;
}

.phone-text {
    color: #1D48CE;
    font-size: 16px;
    font-weight: 500;
}

.width-auto {
    width: auto;
}

.brand-logo {
    width: 13rem;
    height: 2rem;
}

.price-period {
    color: #007bff;
    font-size: 16px;
    font-weight: normal;
}

.broadband-packages {
    display: grid;
    grid-template-columns: 1.2fr 1.8fr;
    gap: 10px;
    width: 100%;
    max-width: 100%; /* 防止父容器宽度限制 */
    box-sizing: border-box; /* 确保边框和内边距不影响宽度 */
}

/* 左侧大卡片 */
.left-package {
    background-color: #fff;
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 右侧布局 */
.right-packages {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100%;
}

.left-package .border-div {
    height: 1px;
    background: #D2D2D2;
}

/* 移动端broadband-packages等高适配 */
@media (max-width: 768px) {
  .top-package .d-flex {
    display: flex;
    align-items: stretch;
}
.top-package-img {
    width: 30vw; /* 宽度自适应屏幕，防止挤压左侧 */
    max-width: 120px; /* 最大宽度限制 */
    height: 91px; /* 固定高度，兼容所有机型 */
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: cover; /* 等比例填充且不拉伸，可能有裁剪 */
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 0;
    margin: 0;
    padding: 0;
    min-width: 60px;
    display: block;
    overflow: hidden;
}
.top-package .pb-2 {
    flex: 1 1 0;
    min-width: 0;
    overflow: hidden;
    word-break: break-all;
}
  .top-package-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    aspect-ratio: 375 / 91;
  }
    object-fit: contain; /* 保证图片完整显示不裁剪 */
    display: block;
    margin: 0;
    padding: 0;
    border-radius: 0;
    background: transparent;
  }
  .top-package {
    padding: 0;
  }

  .broadband-packages {
    display: grid !important;
    grid-template-columns: 1.2fr 1.8fr;
    gap: 10px;
    align-items: stretch;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
  .left-package {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
  }
  .right-packages {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100%;
    justify-content: stretch;
  }
  .top-package {
    flex: 1 1 0;
    min-height: 0;
  }

/* 顶部套餐 */
.top-package {
    background: #fff;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: row; /* 图片和文字并排 */
    width: 100%;
}

/* 右侧图片调整 */
.top-package-img {
    /*width: 50%;*/
}

.top-package-img img {
    /*object-fit: cover;*/
}

/* 调整右侧部分其他样式，确保布局不被影响 */
.top-package {
    background: #fff;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* 底部套餐容器 */
.bottom-packages {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    flex: 1;
}

/* 底部小套餐 */
.bottom-package {
    border: 1px solid #eaeaea;
    border-radius: 8px;
    padding: 15px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 套餐标题样式 */
.package-name .fs-7 {
    font-size: .7rem !important;
}

/* 描述文字 */
.desc-text {
    font-size: .5rem;
    color: #818181;
    font-weight: bold;
}

/* 套餐大数字(100M、300M) */
.big-number {
    font-size: 60px;
    font-weight: bold;
    color: #0066cc;
    line-height: 1;
}

/* 速率单位 */
.speed-unit {
    font-size: 20px;
    color: #0066cc;
    vertical-align: top;
}

/* 年份标记 */
.year-mark {
    font-size: 20px;
    color: #0066cc;
    vertical-align: top;
    position: relative;
    top: 5px;
}

.rice-label {
    font-size: .6rem;
}

/* 价格数字 */
.price-number {
    font-size: .7rem;
    color: #FF0000;
}

/* 年期标记 */
.year-term {
    font-size: .6rem;
    color: #005DF3;
}

.action-btn {
    height: 1.3rem;
    background-color: #005DF3;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: .5rem;
}
.weui-btn_primary{
    background-color: #005DF3 !important;
}
.card, .card-header:first-child {
    border-radius: 0;
}

.card-header {
    background: #005DF3;
    color: #ffffff;
}

.card-header .card-text {
    color: #ffffff;
}

.card-text {
    color: #666666;
}

.border-reset {
    border: 1px solid #E3E9F3;
}

.btn-border {
    color: #1D48CE;
    border: 1px solid #1137BF;
}

.btn-border:hover {
    outline: none;
    color: #1D48CE;
    box-shadow: 0 0 5px #1D48CE !important; /* 聚焦或悬停时的视觉反馈 */
}

.btn-border:active {
    color: #1D48CE !important;
    border: 1px solid #1137BF !important;
}

.pricing {
    margin: 40px 0;
}

.pricing .card {
    border-radius: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.pricing .card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.enterprise-services {
    background: var(--secondary-color);
}

.enterprise-services .fs-6 {
    font-size: .8rem !important;
}

.enterprise-services .fs-6-color {
    color: #1655D9 !important;
}

footer {
    background: #005DF3;
    color: #fff;
    text-align: center;
    font-size: .8rem;
}

footer a {
    text-decoration: none;
}

.modal-content-bg {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    color: inherit; /* 继承全局字体颜色 */
    pointer-events: auto;
    background-color: transparent; /* 移除默认背景色 */
    background-clip: border-box; /* 根据需要调整 */
    border: none; /* 移除边框 */
    outline: 0;
    border-radius: 15px;
    padding: 0 .5rem;
}

.modal-header {
    padding: 0;
    border-bottom: none;
    margin-bottom: -2px;
    position: relative;
}

.modal-header img {
    width: 100%;
}

.modal-header .btn-close-icon {
    position: absolute;
    right: -.5rem;
    top: 6.9rem;
    width: 50px;
    height: 50px;
    padding: calc(var(--bs-modal-header-padding-y) * .5) calc(var(--bs-modal-header-padding-x) * .5);
    margin: calc(-10.5 * var(--bs-modal-header-padding-y)) calc(-.7 * var(--bs-modal-header-padding-x)) calc(-.5 * var(--bs-modal-header-padding-y)) auto;
}

.modal-body {
    margin-left: .8px;
    background: #ffffff;
    padding: 0 30px 30px;
    border-radius: 0 0 15px 15px;
}

/* 表单输入框样式 */
.form-icon {
    position: absolute;
    top: 6px;
    left: 15px;
}

.modal-body .form-control {
    padding-left: 40px;
}

/* 提交按钮样式 */
.modal-body .submit-btn {
    background-color: #ff4d4d;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 16px;
}

.modal-body .submit-btn:hover,
.modal-body .submit-btn:active {
    background-color: #ff4d4d !important;
    color: #fff !important;
}

/*订单详情*/
.order-section {
    padding: 20px 0;
}

.section-title {
    color: #0247D6;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
}

.order-section .form-control {
    border-radius: 4px;
    padding: 8px 12px;
}

.order-section .form-label {
    margin-bottom: 8px;
    font-weight: normal;
}

.order-section .form-control, .order-section .form-select {
    height: 38px; /* 让输入框和下拉框高度一致 */
}

.order-section .col-form-label {
    white-space: nowrap; /* 防止 label 换行 */
}

.order-section .form-select {
    appearance: none; /* 去除默认样式 */
    background-color: #fff;
    border: 1px solid #ced4da;
}

.order-section .row > div {
    padding-right: 5px; /* 适当调整间距，保证整齐 */
}

.product-section {
    padding: 10px 0 30px;
    position: relative;
}

.product-header {
    text-align: center;
    background: url(../img/jst/order-bg.png) no-repeat center center;
    background-size: contain;
    padding: 5px 0;
    color: white;
    position: relative;
    margin-left: auto;
    margin-right: auto;
}

.product-header img {
    width: auto;
    height: 30px;
}

.product-card-container {
    flex: 1 1 calc(33.33% - 10px); /* 每个卡片占三分之一宽度，减去间距 */
    box-sizing: border-box; /* 确保内边距和边框不影响宽度 */
    display: flex; /* 启用 flexbox，使卡片的高度等比 */
    flex-direction: column; /* 卡片内容从上到下排列 */
    height: 100%; /* 使容器的高度自适应 */
}

.product-card-container:nth-child(2) {
    margin: 0 5px;
}

.product-card {
    max-width: 100%;
    height: 100%;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    background-color: white;
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}



.product-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: .8rem;
    min-height: 30.7px; /* 统一高度，可根据实际视觉调整 */
}

.monthly-price {
    font-size: .6rem;
    margin-bottom: 5px;
    min-height: 30.7px; /* 统一高度，可根据实际视觉调整 */
}

.total-price {
    color: #0247D6;
    font-size: .7rem;
    min-height: 30.7px; /* 统一高度，可根据实际视觉调整 */
}

.product-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: .8rem;
    min-height: 30.7px; /* 统一高度，可根据实际视觉调整 */
}

.monthly-price {
    font-size: .6rem;
    margin-bottom: 5px;
    min-height: 30.7px; /* 统一高度，可根据实际视觉调整 */
}

.total-price {
    color: #0247D6;
    font-size: .7rem;
    min-height: 30.7px; /* 统一高度，可根据实际视觉调整 */
}

.price-tag {
    background-color: #0247D6;
    border-radius: 20px;
    padding: 1px 8px;
    color: #fff;
    font-size: .6rem;
}

.bandwidth {
    color: #7EA8F4;
    font-weight: bold;
}

.product-card.selected {
    background: #4F85E6;
    box-shadow: 0 0 10px rgba(13, 110, 253, 0.3);
    color: #fff;
}

.product-card.selected > .monthly-price, .product-card.selected > .total-price {
    color: #fff !important;
}

.product-card.selected .price-tag {
    background-color: #fff;
    color: #0247D6;
}

.product-card.selected .bandwidth {
    color: #fff !important;
}

.product-section .submit-btn {
    width: 50%;
    background-color: #0247D6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    margin-top: 20px;
    padding: 10px 20px;
}

/*订单状态*/
.order-status-section {
    margin: 0 auto;
    padding: 20px;
}

.success-icon img {
    width: 100px;
    height: 100px;
}

.success-header {
}

.success-message {
    font-size: 1.25rem;
    margin-bottom: 0;
}

.amount-value {
    color: #ff0000;
    font-weight: bold;
    font-size: 1.2rem;
}

.total-amount {
    text-align: center;
    position: relative; /* 让伪元素基于它定位 */
}

.total-amount::after {
    content: "";
    display: block;
    width: 100%; /* 控制虚线宽度 */
    height: 1px;
    border-bottom: 2px dashed #000000;
    margin-top: 40px;
}

.order-details {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 15px 0px;
}

.detail-label {
    text-align: left;
}

.detail-value {
    text-align: left;
    margin-left: 20px;
}

.amount {
    color: #908F8F;
    font-size: 13px;
}

/*订单查询*/
.order-query .form-control {
    padding: 8px;
    font-size: 16px;
    border: 1px solid #ced4da;
    border-radius: 5px;
}

.order-query .order-query-btn {
    padding: 8px;
    font-size: 18px;
    border-radius: 5px;
    background: linear-gradient(90deg, rgba(0, 93, 243, 0.78), rgba(0, 185, 255, 0.78)) !important;
    border: none;
    transition: all 0.3s ease-in-out;
}

.js_reset {
    width: auto;
    height: 30px;
    right: 7px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

/*业务受理信息*/
.order-detail-container {
    background-color: #F0F6FD;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 900px;
    margin: 0 auto;
}

.order-tabs {
    padding: 40px 20px 0 20px;
    display: flex;
}

.order-tab-container {
    display: inline-flex;
    margin-right: 10px;
    position: relative;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    border-radius: 30px;
    font-size: 0;
    background: #fff;
}

.order-tab {
    display: inline-block;
    padding: 8px 20px;
    cursor: pointer;
    background-color: #fff;
    color: #666;
    position: relative;
    border: none;
    font-size: 1rem; /* 恢复字体大小 */
}

.order-tab:first-child {
    border-radius: 30px;
    z-index: 2;
}

.order-tab:last-child {
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    z-index: 1;
}

.order-tab.active {
    background-color: #0247D6;
    color: white;
}

.order-tab.active:last-child {
    border-radius: 30px;
}

.order-content {
    padding: 20px;
}

.order-title {
    font-size: 1.1rem;
    text-align: center;
    padding: 15px 0;
    position: relative;
    color: #6B6B6B;
}

.order-title::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: #4F85E6;
}

.order-info {
    margin-top: 20px;
}

.info-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    color: #6B6B6B;
    font-size: .9rem;
}

.info-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    background: #fff;
    font-size: .8rem;
}

.info-table td {
    padding: 10px;
    border: 1px solid #eee;
}

.info-table td:nth-child(odd) {
    width: 17%;
    color: #6B6B6B;
}

.info-table td:nth-child(even) {
    width: 35%;
}

.order-total {
    text-align: right;
    padding: 15px 24px;
    color: #6B6B6B;
}

.price {
    color: #ff4d4f;
    font-size: 18px;
}
.qr-code {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px; /* 设置二维码之间的间距 */
    background: url("../img/jstm/qrCodeModal-bg.png");
}

.qr-code img {
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.8); /* 白色半透明背景 */
    padding: 10px; /* 给二维码增加一些内边距 */
    border-radius: 10px; /* 让背景有圆角 */
}

.qr-code span {
    font-size: .9rem;
    color: #fff;
    text-align: center;
    display: block;
    padding: 10px 0;
}
.form-select {
     --bs-form-select-bg-img: none !important;
    padding: .375rem .25rem .375rem .75rem !important;;
}

.clue-section{
    position: relative;
    /*width: 100vw;*/
    /*height: 100vh;*/
    /*background: url(../img/jstm/clue-bg.png);*/
    /*background-size: cover;*/
}
.clue-section .clue-logo{
    position: absolute;
    top:2%;
    left: 3%;
    width: 8rem;
}
.clue-section .clue-text{
    caret-color: white; /* 光标颜色 */
    position: absolute;
    bottom:26%;
    left: 26%;
    border: none;
    background: none;
}
.clue-section .clue-text:focus {
    outline: none; /* 移除焦点时的轮廓 */
}
.clue-section .oneClickProcessing{
    position: absolute;
    bottom:21%;
    left: 38%;
    width: 6rem;
    height: auto;
}