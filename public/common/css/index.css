:root {
    --primary-color: #005DF3;
    --secondary-color: #EFF6FD;
    --text-color: #ffffff;
    --font-family: Arial, sans-serif;
}
a{
    text-decoration: none;
}
img{
    width: 100%;
}
.container{
    width: 1100px;
}
.brand-image{
    width: 114px;
    height: 32px;
}
.right-logo img{
    width: 25px;
    height: 25px;
}
.top-bar {
    background-color: #fff;
    color: #005DF3;
    text-shadow: none !important;
}
.top-bar span {
    color: #005DF3;
}
.top-bar .d-flex {
    flex-wrap: nowrap;
}
.top-bar a {
    text-decoration: none;
    white-space: nowrap;
}
.service-text::before {
    content: "";
    display: inline-block;
    width: 1px;
    height: 20px;
    background: #005DF3;
    margin-right: 8px;
    vertical-align: middle;
}
.nav-blue{
    background: #005DF3;
    cursor: pointer;
}
.nav-blue .text-line{
    border-bottom: 2px solid #fff;
}
.follow-us {
    position: relative;
    display: inline-block;
}

.qr-code {
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translateX(-50%);
    background: #fff;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    display: none; /* 默认隐藏 */
    z-index: 1000;
    white-space: nowrap; /* 避免换行 */
}

.qr-code .text-center {
    text-align: center;
    margin: 0 10px; /* 增加间距 */
}

.qr-code img {
    width: 120px;
    height: 120px;
    display: block;
}

.qr-code span {
    font-size: 14px;
    color: #333;
}

/* 鼠标划过时显示二维码 */
.follow-us:hover .qr-code {
    display: block;
}

.carousel-container {
    width: 1100px;
    margin:auto;
    position: relative;
}
.carousel-item img {
    width: 100%;
    height: auto;
}
.right-panel {
    position: absolute;
    top: 50%;
    right: 18px;
    transform: translateY(-50%);  /* 垂直居中 */
    width: 320px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 10px;
    backdrop-filter: saturate(180%) blur(14px);
    -webkit-backdrop-filter: saturate(180%) blur(14px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.right-panel img.brand-img {
    width: 100%;
    /*border-radius: 12px;*/
    margin-bottom: 20px;
}
.right-panel .mobile-input{
    text-align: center;
}
.operator-logos {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
}
.operator-logos img {
    height: 28px;
}
.operator-labels {
    text-align: center;
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}
.input-group input {
    border-radius: 50px;
    height: 40px;
    padding-left: 20px;
    box-shadow: none;
}
.btn-danger {
    border-radius: 50px;
    width: 50%;
    height: 37px;
    font-size: 18px;
    line-height: 37px;
    display: flex;
    margin: auto;
    justify-content: center;
    align-items: center;
}
.pricing-section {
    background: var(--secondary-color);
}
.tel-link{
    text-decoration:none;
}
.mt-6{
    margin-top: 5.1rem !important;
}
.color-white{
    color: #ffffff !important;
}
.color-gery{
    color: #505050;
}
.color-gery::before {
    content: "";
    display: inline-block;
    width: 1px;
    height: 20px;
    background: #505050;
    margin-right: 8px;
    vertical-align: middle;
}
.color-wait{
    color: #6E6E6E;
}
.hot-img{
    width: 25px;
    height: 28px;
    vertical-align: middle;
    margin-top: -2px; /* 视觉微调 */
}
.popularity-img{
    width: auto;
    height: 31px;
    vertical-align: middle;
    margin-top: -2px; /* 视觉微调 */
}
.title-wrapper {
    font-size: 1.25rem; /* fs-5 对应的20px */
    line-height: 1; /* 重置行高 */
}

/* 副标题样式 */
.subtitle {
    font-size: 1rem; /* fs-6 对应的16px */
    font-weight: normal;
    position: relative;
    padding-left: 12px; /* 分隔线位置 */
    display: inline-flex;
    align-items: center;
}
/* 分隔线（替换伪元素方案） */
.subtitle::before {
    content: "";
    position: absolute;
    left: 0;
    height: 20px;
    width: 1px;
    background: #505050;
    transform: translateY(-1px); /* 微调垂直居中 */
}
.pricing-card {
    background-color: #EFF6FD;
    padding: 20px;
    text-align: center;
    margin-bottom: 16px;
}
.desc-text {
    font-size: 16px;
    color: #333;
    margin-top: 10px;
    margin-bottom: 20px;
}
.small-title {
    color: #333;
    margin-top: 20px;
}
.btn-group-custom {
    display: inline-flex;
    border: 1px solid #cccccc;
    border-radius: 50px;
    overflow: hidden;
    background: #fff;
}

.btn-group-custom button {
    border: none;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-left {
    background-color: #ffffff;
    color: #333333;
}

.btn-right {
    background-color: #007bff;
    color: #ffffff;
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
    /* 左边圆角要保留，使圆形视觉连续 */
    border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;
}

.btn-left:hover {
    background-color: #f7f7f7;
}

.btn-right:hover {
    background-color: #0056b3;
}
.side-logo{
    width: 410px !important;
}
.phone-text{
    color: #1D48CE;
    font-size: 16px;
    font-weight: 500;
}

.width-auto {
    width: auto;
}

.brand-logo {
    width: auto;
    height: 60px;
}
.price-period {
    color: #007bff;
    font-size: 16px;
    font-weight: normal;
}
.broadband-grid {
    display: flex;
    gap: 20px;
    align-items: stretch;
}
.left-card {
    width: 350px;
    flex: 0 0 38%;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}
.left-card, .right-card {
    background: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    transition: all 0.3s ease;
}
.left-card img {
    width: 100%;
    height: auto;
    object-fit: cover;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.right-card .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.right-card .content .price-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
}
.left-card h4,.left-card .price-line {
    margin: 5px 40px;
}
.product_title {
    color: #818181;
    font-size: 12px;
    margin: 8px 40px 10px;
}
.right-card .ml {
    margin-top: 10px;
}
.left-card .ml{
    margin-left: 40px;
}
.left-card .price-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.broadband-grid .btn {
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.broadband-grid .btn:hover {
    background-color: #0069d9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.25);
}
.right-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
}
.right-card {
    flex-direction: row;
    display: flex;
    align-items: center;
}
.right-card img {
    width: 18.5vw;
    height: auto;
    object-fit: cover;
    margin-right: 20px;
    transition: transform 0.3s ease;
}
.right-card .content {
    flex: 1;
}
.right-card .content .price-line {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 10px;
}
.right-card .content h4, .right-card .content p {
    margin: 5px 0;
}
.price-line strong {
    color: #E60012;
    font-size: 20px;
    font-weight: 700;
}
.card,.card-header:first-child{
    border-radius:0;
}
.card-header{
    background: #005DF3;
    color: #ffffff;
}
.card-header .card-text{
    color: #ffffff;
}
.card-text{
    color: #666666;
}
.border-reset {
    border: 1px solid #E3E9F3;
}
.btn-border{
    color: #1D48CE;
    border: 1px solid #1137BF;
}
.btn-border:hover {
    outline: none;
    color: #1D48CE;
    box-shadow: 0 0 5px #1D48CE !important; /* 聚焦或悬停时的视觉反馈 */
}
.btn-border:active{
    color: #1D48CE !important;
    border: 1px solid #1137BF !important;
}
.pricing {
    margin: 40px 0;
}
.pricing .card {
    border-radius: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
}
.pricing .card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}
.features {
    height: 800px;
    background: url(../img/jst/content_bg.png) no-repeat center;
    background-size: cover;
    box-sizing: border-box; /* 确保 padding 不影响背景图区域 */
}
.features .bg-white {
    border-radius: 15px;
}
.contact-section {
    padding: 40px 0;
}
.custom-card {
    text-align: center; /* 文字居中 */
    color: #ffffff; /* 文字颜色 */
}
.custom-icon img{
    height: 50px;
    width: 50px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-title {
    margin: 5px 0;
    font-size: 18px;
    font-weight: bold;
}

.custom-text {
    font-size: 14px;
}
.module-three{
    background: #EFF6FD !important;
}
.features-section{
    transform: translateY(-330px);
    padding-bottom: 60px;
}
.features-section-mt{
    margin-top: -330px;
}
.features-message{
    background: url(../img/jst/features-message-bg.png) no-repeat center center;
    background-size: cover; /* 确保背景图覆盖整个区域 */
    width: 100%; /* 占满父容器宽度 */
    height: 534px; /* 设置合理的高度，根据需要调整 */
    display: flex; /* 使用 flex 布局 */
    justify-content: right; /* 将文字内容推到右侧 */
    align-items: center; /* 文字垂直居中 */
    padding-right: 50px; /* 控制文字距离右侧的间距 */
    box-sizing: border-box; /* 包含 padding 在内 */
    color: #ffffff; /* 确保文字颜色清晰 */
}
.features-message h2 {
    margin-bottom: 1rem;
    line-height: 3;
}
.features-message p {
    font-size: 1rem;
    text-align: left;
    line-height: 2;
}
.section-3 .card-header {
    background-color: #005DF3;
    color: white; /* 标题文字颜色 */
    border-bottom: none;
}
.section-3 .border-reset{
    border: none;
}
.card-line-short {
    position: relative;
    margin-bottom: 10px; /* 保证和其他内容有间距 */
}

.card-line-short::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -5px; /* 线条距离文字的距离 */
    transform: translate(-50%,-50%);
    width: 13%; /* 线条宽度与文字宽度一致 */
    height: 2px; /* 线条厚度 */
    background-color:#E44E4B; /* 线条颜色 */
}
.card-line-long {
    position: relative;
    margin-bottom: 10px; /* 保证和其他内容有间距 */
}

.card-line-long::after {
     content: '';
     position: absolute;
     left: 50%;
     bottom: -5px; /* 线条距离文字的距离 */
     transform: translate(-50%,-50%);
     width: 16%; /* 线条宽度与文字宽度一致 */
     height: 2px; /* 线条厚度 */
     background-color:#203B90; /* 线条颜色 */
 }
.card-line-long2::after {
    background-color:#1163CE; /* 线条颜色 */
}

.section-3 .card-body {
    position: relative; /* 确保伪元素基于该容器定位 */
}

.section-3 .card-body::before {
    content: ''; /* 必须设置内容 */
    position: absolute; /* 定位到 card-body */
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 12px 12px 0; /* 三角形宽高，具体尺寸可调整 */
    border-color: transparent #005DF3 transparent transparent; /* 背景色与 card-header 一致 */
}
.module-five{
    width: 100%;
    height: 400px;
    background: url("../img/jst/module-five-bg.png");
    background-size: 100% 400px;
}
.module-five p{
    margin: 0;
    line-height: 2.2;
}
footer {
    background: #005DF3;
    color: #fff;
    text-align: center;
}
footer a{
    text-decoration: none;
}
.modal-content-bg {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    color: inherit; /* 继承全局字体颜色 */
    pointer-events: auto;
    background-color: transparent; /* 移除默认背景色 */
    background-clip: border-box; /* 根据需要调整 */
    border: none; /* 移除边框 */
    outline: 0;
    padding: 0; /* 移除默认内边距 */
    border-radius: 15px;
  }
  .modal-header {
    padding: 0;
    border-bottom: none;
    margin-bottom: -2px;
  }
  .modal-header img{
    width: 100%;
  }
  .modal-header .btn-close-icon{
    width: 50px;
    height: 50px;
    padding: calc(var(--bs-modal-header-padding-y)* .5) calc(var(--bs-modal-header-padding-x)* .5);
    margin: calc(-10.5* var(--bs-modal-header-padding-y)) calc(-.7* var(--bs-modal-header-padding-x)) calc(-.5* var(--bs-modal-header-padding-y)) auto;
    cursor: pointer;
}
.modal-body{
    margin-left: .8px;
    background: #ffffff;
    padding: 0 30px 30px;
    border-radius:0 0 15px 15px;
}
  /* 表单输入框样式 */
  .form-icon {
    position: absolute;
    top: 6px;
    left: 15px;
  }

  .form-control {
    padding-left: 40px;
  }

  /* 提交按钮样式 */
  .modal-body .submit-btn {
    background-color: #ff4d4d;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 16px;
  }
  .modal-body .submit-btn:hover,
  .modal-body .submit-btn:active{
    background-color: #ff4d4d !important;
    color: #fff !important;
  }
  /*订单详情*/
.order-section {
    /*padding: 30px 0;*/
}
.section-title {
    color: #0247D6;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
}
.order-section .form-control {
    border-radius: 4px;
    padding: 8px 12px;
}
.order-section .form-label {
    margin-bottom: 8px;
    font-weight: normal;
}
.order-section .form-control, .order-section .form-select {
    height: 38px; /* 让输入框和下拉框高度一致 */
}

.order-section .col-form-label {
    white-space: nowrap; /* 防止 label 换行 */
}

.order-section .form-select {
    appearance: none; /* 去除默认样式 */
    background-color: #fff;
    border: 1px solid #ced4da;
}

.order-section .row > div {
    padding-right: 5px; /* 适当调整间距，保证整齐 */
}

.product-section {
    padding: 10px 0 30px;
    position: relative;
}
.product-header {
    text-align: center;
    background: url(../img/jst/order-bg.png) no-repeat center center;
    background-size: contain;
    padding: 5px 0;
    color: white;
    position: relative;
    margin-left: auto;
    margin-right: auto;
}
.product-header img {
    width: auto;
    height: 30px;
}
.product-card {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 15px;
    background-color: white;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}
.product-title {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 18px;
}
.monthly-price {
    font-size: 14px;
    margin-bottom: 5px;
}
.total-price {
    color: #0247D6;
    display: flex;
    align-items:center; /* 让所有子元素垂直居中 */
    gap: 10px; /* 控制间距 */
}
.price-tag {
    background-color: #0247D6;
    border-radius: 15px;
    padding: 3px 10px;
    color: #fff;
    font-size: 12px;
    display: inline-block;
    margin-left: 5px;
}
.bandwidth {
    color: #7EA8F4;
    font-weight: bold;
}
.product-card.selected{
    background: #4F85E6;
    box-shadow: 0 0 10px rgba(13, 110, 253, 0.3);
    transform: translateY(-5px);
    color: #fff;
}
.product-card.selected > .monthly-price,.product-card.selected > .total-price{
    color: #fff !important;
}
.product-card.selected .price-tag{
    background-color: #fff;
    color: #0247D6;
}
.product-card.selected .bandwidth{
    color: #fff !important;
}
.product-section .submit-btn {
    width: 40%;
    background-color: #0247D6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    margin-top: 20px;
    padding: 10px 20px;
}
/*订单状态*/
.order-status-section {
    margin: 0 auto;
    padding: 20px;
}
.success-icon img{
    width: 80px;
    height: 80px;
}
.success-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}
.success-message {
    font-size: 1.45rem;
    margin-bottom: 0;
}
.amount-value {
    color: #ff0000;
    font-weight: bold;
    font-size: 1.2rem;
}
.total-amount {
    text-align: right; /* 左对齐 */
    position: relative; /* 让伪元素基于它定位 */
    margin-left: 88px; /* 让它和 success-message 文字对齐 */
}

.total-amount::after {
    content: "";
    display: block;
    width: 100%; /* 控制虚线宽度 */
    height: 1px;
    border-bottom: 2px dashed #000000;
    margin-top: 10px;
}
.order-details {
    display: grid;
    grid-template-columns: 100px 1fr;
    gap: 15px 10px;
}
.detail-label{
    text-align: left;
}
.detail-value{
    text-align: left;
    margin-left: 20px;
}
.amount{
    color: #908F8F;
    font-size: 13px;
}
/*订单查询*/
.order-query-section {
    background: url("../img/jst/order-query-bg.png") no-repeat center top;
    background-size: cover;
}
.order-query .form-control {
    padding: 8px;
    font-size: 16px;
    border: 1px solid #ced4da;
    border-radius: 5px;
}

.order-query .order-query-btn {
    padding: 8px;
    font-size: 18px;
    border-radius: 5px;
    background: linear-gradient(90deg, rgba(0,93,243,0.78), rgba(0,185,255,0.78)) !important;
    border: none;
    transition: all 0.3s ease-in-out;
}

.js_reset {
    width: auto;
    height: 30px;
    right: 7px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}
/*业务受理信息*/
.order-detail-container {
    background-color: #F0F6FD;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 900px;
    margin: 0 auto;
}

.order-tabs {
    padding: 40px 20px 0 20px;
    display: flex;
}

.order-tab-container {
    display: inline-flex;
    margin-right: 10px;
    position: relative;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    border-radius: 30px;
    font-size: 0;
    background: #fff;
}

.order-tab {
    display: inline-block;
    padding: 8px 20px;
    cursor: pointer;
    background-color: #fff;
    color: #666;
    position: relative;
    border: none;
    font-size: 1rem; /* 恢复字体大小 */
}

.order-tab:first-child {
    border-radius: 30px;
    z-index: 2;
}

.order-tab:last-child {
    border-top-right-radius: 30px;
    border-bottom-right-radius: 30px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    z-index: 1;
}

.order-tab.active {
    background-color: #0247D6;
    color: white;
}
.order-tab.active:last-child{
    border-radius: 30px;
}

.order-content {
    padding: 20px;
}

.order-title {
    font-size: 23px;
    text-align: center;
    padding: 15px 0;
    position: relative;
    color: #6B6B6B;
}

.order-title::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color: #4F85E6;
    margin: 0 -20px;
    width: calc(100% + 40px);
}

.order-info {
    margin-top: 20px;
}

.info-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    color: #6B6B6B;
}

.info-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    background: #fff;
}

.info-table td {
    padding: 10px 15px;
    border: 1px solid #eee;
}

.info-table td:nth-child(odd) {
    width: 15%;
    color: #6B6B6B;
}

.info-table td:nth-child(even) {
    width: 35%;
}

.order-total {
    text-align: right;
    padding: 15px 24px;
    color: #6B6B6B;
}

.price {
    color: #ff4d4f;
    font-size: 18px;
}
