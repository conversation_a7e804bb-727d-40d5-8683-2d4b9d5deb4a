$(document).ready(function () {
    function setPopularityUrl(element, ad_link, jumpType) {
        if (ad_link) {
            element.prop('href', ad_link);
        } else if (jumpType === "1") {
            element.prop('href', 'https://affim.baidu.com/unique_64163745/chat?siteId=21656422&userId=64163745&siteToken=7ca96115e97d4825193127f600216f42');
        } else if (jumpType === "2") {
            element.attr({"data-bs-toggle": "modal", "data-bs-target": "#exampleModal"});
        }
    }

    setPopularityUrl($(".popularity-url-top"), $("input[name='ad_link']").val(), $("input[name='jump_type']").val());

    function bindSubmitEvent(buttonId, sourceValue, ...fields) {
        console.log(sourceValue);
        $(buttonId).click(function () {
            $('[name=source]').val(sourceValue);
            CreateClue(...fields.map(field => $(field).val()));
        });
    }

    bindSubmitEvent('#js-submit', 1, '[name=consignee_tel]', '[name=address]');
    bindSubmitEvent('.oneClickProcessing', 1, '[name=oneClickProcessing_consignee_tel]');
    bindSubmitEvent('#submit-drpecn', 2, '[name=consignee_tel2]', '[name=address2]', '[name=company_name]', '[name=remark]');
    $('.kefu-link').prop('href', navigator.userAgent.toLowerCase().match(/iphone|ipod|android|blackberry|windows phone|webos|mobile/i)
        ? 'https://affim.baidu.com/unique_64163745/mobile/chat?siteId=21656422&userId=64163745&siteToken=7ca96115e97d4825193127f600216f42'
        : 'https://affim.baidu.com/unique_64163745/chat?siteId=21656422&userId=64163745&siteToken=7ca96115e97d4825193127f600216f42');

    $('.go-handle').click(function () {
        window.location.href = '/orderCreat?id=' + btoa($(this).data('id').toString());
    });

    function updatePopularityUrls() {
        $(".popularity-col").each(function () {
            setPopularityUrl($(this).find(".popularity-url"), $(this).find("input[name='ad_link']").val(), $(this).find("input[name='jump_type']").val());
        });
    }

    updatePopularityUrls();
    $('.advertisement-btn').click(updatePopularityUrls);
});
