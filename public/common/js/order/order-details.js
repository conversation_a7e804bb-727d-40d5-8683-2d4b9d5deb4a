$(document).ready(function() {
    // 根据catId切换产品标题和图片的数据字典
    const productTypeDict = {
        '1031': {
            title: '宽带通产品套餐',
            imgSrc: '/common/img/jst/kdt-order.png',
            imgAlt: 'Wifi'
        },
        '1032': {
            title: '联通产品套餐',
            imgSrc: '/common/img/jst/lt-order.png',
            imgAlt: '联通'
        },
        '1033': {
            title: '电信产品套餐',
            imgSrc: '/common/img/jst/dx-order.png',
            imgAlt: '电信'
        },
        '1034': {
            title: '移动产品套餐',
            imgSrc: '/common/img/jst/yd-order.png',
            imgAlt: '移动'
        }
    };
    let dxAddress='';
    // 获取URL参数的函数
    $.getUrlParam = function(name) {
        const results = new RegExp('[?&]' + name + '=([^&#]*)').exec(window.location.href);
        return results ? decodeURIComponent(results[1]) : null;
    };

    const productId = $.getUrlParam('id');
    let decodedId = atob(productId);

    // 处理产品卡片选择和更新产品标题与图片
    $(".product-card").each(function() {
        let productId = $(this).data('product-id');
        let catId = $(this).data('cat-id');

        if (decodedId == productId) {
            $(this).addClass('selected');
            updateProductHeader(catId);
        }
    });

    // 根据catId更新产品标题和图片的函数
    function updateProductHeader(catId) {
        if (productTypeDict[catId]) {
            const productInfo = productTypeDict[catId];
            $('.product-header').html(`
                <img src="${productInfo.imgSrc}" alt="${productInfo.imgAlt}" class="d-inline">
                <span class="">${productInfo.title}</span>
            `);
        }
    }

    // 手机号输入限制
    $("#phone").on("input", function() {
        $(this).val($(this).val().replace(/\D/g, "").substring(0, 11));
    });

    let selectedProduct = null;
    // 产品选择功能
    $('.product-card').on('click', function() {
        $('.product-card').removeClass('selected');
        $(this).addClass('selected');
        selectedProduct = {
            id: $(this).data('product-id'),
            name: $(this).data('product-name'),
            price: $(this).data('price')
        };
    });

    // 下单按钮公共处理函数
    function handleOrder(orderSource,gwbn_url, callback) {
        const phone = $('#phone').val();
        if (!(/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(phone))) {
            _alert('请输入正确的手机号');
            return false;
        }
        if (!$('.product-card').hasClass('selected')) {
            _alert('请先选择一个产品套餐！');
            return;
        }
        const id = $('#certNumber').val().trim();
        if (!validateChineseID(id)) {
            _alert('请填写正确的身份证号码！');
            return;
        }
        const orderData = {
            consignee: $('#name').val(),
            consignee_tel: phone,
            province_id: $('#province').val(),
            city_id: $('#city').val(),
            area_id: $('#district').val(),
            address: $('#address').val(),
            dx_address:dxAddress,
            //relation_cooperate_channel:$('#relation_cooperate_channel').val(),
            certNumber: $('#certNumber').val(),
            product_id: $('.selected').data('product-id'),
            order_source: orderSource
        };
        console.log(orderData)
        if (Object.values(orderData).some(value => !value)) {
            _alert('请填写完整的订单信息！');
            return;
        }

        var config_params = {
            appid: 'xpl_jst_api',
            key: '2nMtwOPSNYBLGE0mvVk2w_j8kBwclBl1',
            params: orderData
        };
        var urlHead = gwbn_url;
        var data = encryption(config_params);
        console.log(config_params, 'config_params');
        $.ajax({
            url: urlHead + '/jst/CreateOrder',
            type: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: {
                data: data
            },
            dataType: 'json',
            success: function (res) {
                if (res.header.code == 200) {
                    window.location.href = '/orderStatus?order_id=' + btoa(res.body.order_id);
                } else {
                    _alert(res.body);
                }
            },
            error: function (err) {
                console.log(err);
            }
        });
    }
    $('#orderButton').on('click', function() {
        // 如果按钮已禁用，直接返回
        if ($(this).prop('disabled')) {
            return;
        }
        
        // 禁用按钮并改变样式
        $(this).prop('disabled', true).addClass('disabled');
        
        // 保存原始文本
        const originalText = $(this).text();
        // 更改按钮文本
        $(this).text('处理中...');
        let gwbn_url =$(this).data('gwbnUrl');
        handleOrder(1,gwbn_url, () => {
        // 完成后恢复按钮状态（无论成功失败）
            $(this).prop('disabled', false).removeClass('disabled').text(originalText);
        });
    });

    $('#orderButton_m').on('click', function() {
        let gwbn_url =$(this).data('gwbnUrl');
        handleOrder(2,gwbn_url);
    });
    $('.address-search').on('click', function() {
        // 只调用 searchAddress，所有 loading 逻辑统一在 searchAddress 内部
        searchAddress();
    });
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    let isSearching = false; // 添加搜索状态标志

    // 搜索地址的函数
    function searchAddress() {
        console.log('searchAddress called');
        const $searchIcon = $('.address-search');
        const address = $('#address').val();

        // 如果已经在搜索中，直接返回
        if (isSearching) {
            console.log('Already searching, return');
            return;
        }

        if (address == '') {
            _alert('请输入地址查询！');
            return;
        }

        isSearching = true; // 设置为正在搜索

        const orderData = {
            address: $('#address').val(),
        };
        var config_params = {
            appid: 'telecom_api',
            key: '2nMtwOPSNYBLGE0mvVk2w_j8kBwclBl1',
            params: orderData
        };

        var urlHead = $searchIcon.data('gwbn-url');
        var data = encryption(config_params);

        // 显示加载状态
        $searchIcon.addClass('loading');
        $searchIcon.find('.bi-search').hide();
        $searchIcon.find('.bi-hourglass').show();
        $.ajax({
            // url: urlHead + '/telecom/addressQuery',
            url: 'http://tapi.bj95079.com.cn/telecom/addressQuery',
            type: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: {
                data: data
            },
            dataType: 'json',
            success: function (res) {
                if (res.header.code == 200) {
                    if (res.body.data.biz.items.length <= 0) {
                        _alert('该地址暂未覆盖');
                        return;
                    }

                    // 清空地址列表
                    $('.address-list').empty();
                    if(res.body.data.biz.items.length<=0){
                        _alert('地址未覆盖');
                        return
                    }
                    // 添加地址列表项
                    res.body.data.biz.items.forEach(function(address) {
                        $('.address-list').append(`
                            <div class="address-item">
                                <div class="address-text">${address}</div>
                            </div>
                        `);
                    });

                    // 显示模态框
                    $('#addressModal').modal('show');

                    // 添加地址选择事件
                    $('.address-item').click(function() {
                        // 移除所有选中状态
                        $('.address-item').removeClass('selected');
                        // 添加当前选中状态
                        $(this).addClass('selected');
                        // 保存选中的地址
                        window.selectedAddress = $(this).find('.address-text').text();
                        dxAddress = $(this).find('.address-text').text();
                        //const addressText = $(this).find('.address-text').text();
                        //const itemList = res.body.data.biz.extInfo.itemList || {};
                        //const labelValue = itemList[addressText] || '';
                        //$('#relation_cooperate_channel').val(labelValue);
                    });

                    // 添加确认按钮事件
                    $('#confirmAddress').click(function() {
                        if (window.selectedAddress) {
                            $('#address').val(window.selectedAddress);
                            $('#addressModal').modal('hide');
                        } else {
                            _alert('请选择一个地址');
                        }
                    });
                } else {
                    _alert(res.body);
                }
            },
            error: function (err) {
                console.log(err);
                _alert('地址查询失败');
            },
            complete: function() {
                // 隐藏加载状态
                $searchIcon.removeClass('loading');
                $searchIcon.find('.bi-search').show();
                $searchIcon.find('.bi-hourglass').hide();
                isSearching = false; // 重置搜索状态
            }
        });
    }
    function validateChineseID(id) {
        const regex = /^[1-9]\d{5}(18|19|20|21)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/;
        if (!regex.test(id)) return false;

        const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        const checkDigits = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        const chars = id.toUpperCase().split('');
        let sum = 0;

        for (let i = 0; i < 17; i++) {
            sum += parseInt(chars[i], 10) * weights[i];
        }

        return checkDigits[sum % 11] === chars[17];
    }

});
