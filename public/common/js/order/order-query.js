$(function () {
    var reset = $('.js_reset');
    var btnFlag = true;
    //点击重置验证码
    reset.on('click', function () {
        var t = new Date().getTime();
        this.src = '/order/captcha?t=' + t;
    });
    //表单验证
    $('.order-query-btn').on('click', function () {
        if (!btnFlag) {
            return false;
        }
        // 获取手机号和验证码的值
        let phone = $('input[name="phone"]').val().trim();
        let captcha = $('input[name="verify"]').val().trim();
        
        // 校验手机号格式
        if (!/^1[3-9]\d{9}$/.test(phone)) {
            _alert("请输入正确的手机号！");
            return false;
        }
        if (captcha === "") {
            _alert("验证码不能为空！");
            return false;
        }
        let formData = {
            phone: phone,
            verify: captcha
        };
        $.ajax({
            url: '/order/orderQueryCheck',
            type: 'post',
            data: formData,
            dataType: 'JSON',
            beforeSend: function () {
                btnFlag = false;
                $('.loading-content').show();
            },
            success: function (res) {
                if (res.code == 200) {
                    window.location.href = res.data;
                } else {
                    _alert(res.msg);
                    return false;
                }
            },
            error: function (err) {
                _alert('当前网络波动，请稍后再试');
            },
            complete: function () {
                btnFlag = true;
                $('.loading-content').hide();
                var t = new Date().getTime();
                reset[0].src = '/order/captcha?t=' + t;
            }
        })
    });
});