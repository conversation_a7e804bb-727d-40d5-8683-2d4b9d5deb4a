:root {
    --primary-color: #193AB1;
    --secondary-color: #EFF6FD;
    --text-color: #ffffff;
    --font-family: Arial, sans-serif;
}

body {
    /*color: var(--text-color);*/
}
#jst_mobile{
    display: none;
}
#jst_pc{
    display: none;
}
.top-bar {
    background-color: var(--primary-color);
}

.pricing-section {
    background: var(--secondary-color);
}
.tel-link{
    text-decoration:none;
}
.mt-6{
    margin-top: 5.1rem !important;
}
.color-white{
    color: #ffffff !important;
}
/* 顶部导航栏 */
.top-bar {
    padding: 10px 0; /* 上下内边距 */
    color: #ffffff; /* 白色字体 */
}

/* LOGO 样式 */
.logo img {
    height: 40px; /* LOGO 高度 */
    max-width: 100%; /* 防止超出容器 */
}

/* 电话图标和文本样式 */
.contact-info {
    font-size: 1rem; /* 字体大小 */
    font-weight: 500; /* 字体粗细 */
}

.contact-info img {
    height: 20px; /* 图标高度 */
    margin-right: 8px; /* 图标和文字间距 */
}

.contact-info span {
    font-size: 1.1rem; /* 电话字体大小 */
}

.header {
    height: 580px;
    background-image: url("../img/bg.png");  /* 设置背景图片 */
    background-size: cover;                  /* 背景图片覆盖整个区域 */
    background-position: center;             /* 背景图片居中 */
    background-repeat: no-repeat;            /* 防止背景图片重复 */
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.3); /* 添加文本阴影 */
}
.pricing-section{
    padding-bottom: 37px;
}
.pricing-section .row{
    /*margin-left: 0;*/
    /*margin-right: 0;*/
}
.pricing-section-mt{
    margin-top:-88px;
}
.pricing-section-mt img{
    width: 100%;
}
.consult-img{
    width: 120px !important;
}
.side-logo{
    width: 410px !important;
}
.message-img{
    width: 100%;
}
.message-position{
    transform: translateY(-58px); /* 向上移动 100px */
    padding-bottom: 21px;
}
.message-form{
    /*background: url("../img/brand-side-bg.png");*/
    /*background-size: cover;*/
}
.js-bo{
    width: 35%;
    padding: 13px 20px;
    border: 1px solid #F33A3C;
    color: #fff;
    background: #F33A3C;
    border-radius:30px;
    cursor: pointer;
    font-weight: bold;
    letter-spacing: 2px;
}
.js-bo:hover {
    outline: none;
    box-shadow: 0 0 5px rgba(243, 58, 60, 0.5); /* 聚焦或悬停时的视觉反馈 */
}
.card,.card-header:first-child{
    border-radius:0;
}
.card-header{
    background: #0347D6;
    color: #ffffff;
}
.card-header .card-text{
    color: #ffffff;
}
.card-text{
    color: #666666;
}
.border-reset {
    border: 1px solid #E3E9F3;
}
.btn-border{
    color: #1D48CE;
    border: 1px solid #1137BF;
}
.btn-border:hover {
    outline: none;
    color: #1D48CE;
    box-shadow: 0 0 5px #1D48CE !important; /* 聚焦或悬停时的视觉反馈 */
}
.btn-border:active{
    color: #1D48CE !important;
    border: 1px solid #1137BF !important;
}
.pricing {
    margin: 40px 0;
}
.pricing .card {
    border-radius: 15px;
    transition: transform 0.3s, box-shadow 0.3s;
}
.pricing .card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}
.features {
    height: 800px;
    background: url(../img/content_bg.png) no-repeat center;
    background-size: cover;
    box-sizing: border-box; /* 确保 padding 不影响背景图区域 */
}
.features .bg-white {
    border-radius: 15px;
}
.contact-section {
    padding: 40px 0;
}
.custom-card {
    text-align: center; /* 文字居中 */
    color: #ffffff; /* 文字颜色 */
}
.custom-icon img{
    height: 50px;
    width: 50px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-title {
    margin: 5px 0;
    font-size: 18px;
    font-weight: bold;
}

.custom-text {
    font-size: 14px;
}
.module-three{
    background: #EFF6FD !important;
}
.features-section{
    transform: translateY(-330px);
    padding-bottom: 60px;
}
.features-section-mt{
    margin-top: -330px;
}
.features-message {
    background: url(../img/features-message-bg.png) no-repeat center center;
    background-size: cover; /* 确保背景图覆盖整个区域 */
    width: 100%; /* 占满父容器宽度 */
    height: 534px; /* 设置合理的高度，根据需要调整 */
    display: flex; /* 使用 flex 布局 */
    justify-content: right; /* 将文字内容推到右侧 */
    align-items: center; /* 文字垂直居中 */
    padding-right: 50px; /* 控制文字距离右侧的间距 */
    box-sizing: border-box; /* 包含 padding 在内 */
    color: #ffffff; /* 确保文字颜色清晰 */
}
/* 文字区域容器，位于蓝色区域右侧 */
.features-message-content {

}
.features-message h2 {
    /*font-size: 2rem;*/
    margin-bottom: 1rem;
    line-height: 3;
}
.features-message p {
    font-size: 1rem;
    text-align: left;
    line-height: 2;
}
.section-3 .card-header {
    background-color: #0347D6;
    color: white; /* 标题文字颜色 */
    border-bottom: none;
}
.section-3 .border-reset{
    border: none;
}
.card-line-short {
    position: relative;
    margin-bottom: 10px; /* 保证和其他内容有间距 */
}

.card-line-short::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -5px; /* 线条距离文字的距离 */
    transform: translate(-50%,-50%);
    width: 13%; /* 线条宽度与文字宽度一致 */
    height: 2px; /* 线条厚度 */
    background-color:#E44E4B; /* 线条颜色 */
}
.card-line-long {
    position: relative;
    margin-bottom: 10px; /* 保证和其他内容有间距 */
}

.card-line-long::after {
     content: '';
     position: absolute;
     left: 50%;
     bottom: -5px; /* 线条距离文字的距离 */
     transform: translate(-50%,-50%);
     width: 16%; /* 线条宽度与文字宽度一致 */
     height: 2px; /* 线条厚度 */
     background-color:#203B90; /* 线条颜色 */
 }
.card-line-long2::after {
    background-color:#1163CE; /* 线条颜色 */
}

.section-3 .card-body {
    position: relative; /* 确保伪元素基于该容器定位 */
}

.section-3 .card-body::before {
    content: ''; /* 必须设置内容 */
    position: absolute; /* 定位到 card-body */
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 12px 12px 0; /* 三角形宽高，具体尺寸可调整 */
    border-color: transparent #0347D6 transparent transparent; /* 背景色与 card-header 一致 */
}
.module-four{
    background: #ffffff !important;
}
.module-four-bg{
    background: url("../img/module-four-bg.png");
    background-size: cover;
}
.module-five{
    background: url("../img/module-five-bg.png");
    background-size: cover;
}
.module-five p{
    margin: 0;
    line-height: 2.2;
}

/* 主容器样式 */
.layout-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    padding: 20px;
    position: relative;
}

/* 标题样式 */
.title h2 {
    font-size: 24px;
    color: #E2E2E2;
}
.title h3 {
    font-size: 28px;
    color: #000;
    margin-bottom: 10px;
    margin-top: -20px;
}
.title p {
    font-size: 16px;
    color: #666;
}

/* 内容区域 */
.content {
    position: relative;
    height: 500px; /* 设置整体高度 */
}

/* 背景图容器 */
.center-image {
    position: relative;
    width: 100%; /* 背景图宽度 */
    height: 500px; /* 背景图高度 */
    margin: 0 auto;
    background: url("../img/module-four-bg.png") no-repeat center;
    background-size: contain; /* 背景图适应容器 */
}
/* 四个内容块样式 */

.box {
    position: absolute;
    width: 210px;
    padding: 10px;
    text-align: left;
}
.box h4 {
    font-size: 18px;
    margin: 0;
    color: #000;
}
.box p {
    font-size: 14px;
    color: #666;
}

/* 定位四个块到虚线框 */
.top-left {
    top: 28px;
    left: 125px;
}
.top-right {
    top: 90px;
    right: 113px;
}
.bottom-left {
    bottom: 28px;
    left: 52px;
}
.bottom-right {
    bottom: 68px;
    right: 45px;
}


footer {
    background: #082DB3;
    color: #fff;
    text-align: center;
    padding: 20px 0;
}
footer a{
    text-decoration: none;
}
.modal-content-bg {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    color: inherit; /* 继承全局字体颜色 */
    pointer-events: auto;
    background-color: transparent; /* 移除默认背景色 */
    background-clip: border-box; /* 根据需要调整 */
    border: none; /* 移除边框 */
    outline: 0;
    padding: 0; /* 移除默认内边距 */
    border-radius: 15px;
  }

  .modal-header {
    padding: 0;
    border-bottom: none;
    margin-bottom: -2px;
  }
  .modal-header img{
    width: 100%;
  }
  .modal-header .btn-close-icon{
    width: 50px;
    height: 50px;
    padding: calc(var(--bs-modal-header-padding-y)* .5) calc(var(--bs-modal-header-padding-x)* .5);
    margin: calc(-10.5* var(--bs-modal-header-padding-y)) calc(-.7* var(--bs-modal-header-padding-x)) calc(-.5* var(--bs-modal-header-padding-y)) auto;
    cursor: pointer;
}
.modal-body{
    margin-left: .8px;
    background: #ffffff;
    padding: 0 30px 30px;
    border-radius:0 0 15px 15px;
}
  /* 表单输入框样式 */
  .form-icon {
    position: absolute;
    top: 6px;
    left: 15px;
  }

  .form-control {
    padding-left: 40px;
  }

  /* 提交按钮样式 */
  .submit-btn {
    background-color: #ff4d4d;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 16px;
  }
  .submit-btn:hover,
  .submit-btn:active{
    background-color: #ff4d4d !important;
    color: #fff !important;
  }
  .tel-p{
      color: #0347D6;
      /*line-height: 70px;*/
  }
  .tel{
      width: 30px !important;
      height: 30px;
      margin-right: 6px;
  }

/* 响应式设计 */
/* 极小屏幕适配 */
@media (max-width: 480px) {
    .consult-img {
        width: 78px !important;
    }
    .features-section{
        transform: translateY(0px);
    }
    .features-section-mt{
        margin-top: 0;
    }
    .js-bo{
        width: 50%;
    }
    .message-form{
        background: none !important;
    }
    .layout-container{
        display: none;
    }
}
/* 小于576px的手机 */
@media (max-width: 576px) {
    .consult-img {
        width: 78px !important;
    }
    .top-bar .container {
        flex-direction: row; /* 保持左右对齐 */
        justify-content: space-between; /* LOGO 和电话分散两边 */
    }
    .features {
        height:auto !important;
    }
    .features-message {
        background: url(../img/features-message-bg.png) no-repeat right center;
        background-size: cover;
    }
    .features-section{
        transform: translateY(0px);
    }
    .features-section-mt{
        margin-top: 0;
    }
    .js-bo{
        width: 50%;
    }
    .module-five{
        background: url("../img/module-five-bg.png") right;
        background-size: cover;
    }
    .ml-3{
        padding-left: 5rem !important;
    }
    .box {
        position: absolute;
        width: 120px;
        padding: 10px;
        text-align: left;
    }
    .box h4 {
        font-size: 14px;
        margin: 0;
        color: #000;
    }
    .box p {
        font-size: 10px;
        color: #666;
    }
    .top-left {
        top: 113px;
        left: 35px;
    }
    .top-right {
        top: 146px;
        right: 24px;
    }
    .bottom-left {
        bottom: 110px;
        left: 12px;
    }
    .bottom-right {
        bottom: 110px;
        right: 6px;
    }
    .modal-content-bg{
        padding: 35px;
    }
    .message-form{
        background: none !important;
    }
    .layout-container{
        display: none;
    }
}

/* 768px到575px之间的平板设备 */
@media (min-width: 577px) and (max-width: 768px) {
    .consult-img {
        width: 78px !important;
    }
    .features-section{
        transform: translateY(0px);
    }
    .features-section-mt{
        margin-top: 0;
    }
    .features-message {
        background: url(../img/features-message-bg.png) no-repeat right center;
        background-size: cover;
    }
    .module-five{
        background: url("../img/module-five-bg.png") right;
        background-size: cover;
    }
    .ml-3{
        margin-left: -5rem;
    }
    .box {
        position: absolute;
        width: 160px;
        padding: 10px;
        text-align: left;
    }
    .box h4 {
        font-size: 16px;
        margin: 0;
        color: #000;
    }
    .box p {
        font-size: 12px;
        color: #666;
    }
    .top-left {
        top: 123px;
        left: 75px;
    }
    .top-right {
        top: 148px;
        right: 64px;
    }
    .bottom-left {
        bottom: 100px;
        left: 32px;
    }
    .bottom-right {
        bottom: 135px;
        right: 20px;
    }
    .modal-content-bg{
        padding: 35px;
    }
    .message-form{
        background: none !important;
    }
    .layout-container{
        display: none;
    }
}

/* 992px到767px之间的平板或小屏幕笔记本 */
@media (min-width: 769px) and (max-width: 992px) {
    .consult-img {
        width: 78px !important;
    }
    .features-message {
        background: url(../img/features-message-bg.png) no-repeat right center;
        background-size: cover;
    }
    .module-five{
        background: url("../img/module-five-bg.png") right;
        background-size: cover;
    }
    .features-section{
        transform: translateY(0px);
    }
    .features-section-mt{
        margin-top: 0;
    }
    .ml-3{
        margin-left: -5rem;
    }
    .top-left {
        top: 103px;
        left: 75px;
    }
    .top-right {
        top: 128px;
        right: 64px;
    }
    .bottom-left {
        bottom: 80px;
        left: 32px;
    }
    .bottom-right {
        bottom: 115px;
        right: 20px;
    }
    .modal-content-bg{
        padding: 35px;
    }
    .message-form{
        background: none !important;
    }
}

/* 大于等于1200px的桌面设备 */
@media (min-width: 1200px) {
    /* 样式 */
}
