function _alert(str, type){
    swal({
        title: "",
        text: str,
        timer: 2000,
        type: type,
        showConfirmButton: false
    });
}
function CreateClue(consignee_tel,address,company_name,remark) {
      if(!(/^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(consignee_tel))) {
        _alert('请输入正确的手机号');
        return false;
    }
   //source:1、家宽，2、政企
    var install_params = {
        consignee_tel:consignee_tel,
        address:address,
        company_name:company_name,
        remark:remark,
        site_id:2,
        source:$('[name=source]').val()
    };
    var config_params = {
        appid: 'xpl_tool_api',
        key: '2nMtwOPSNYBLGE0mvVk2w_j8kBwclBl1',
        params: install_params
    }
    var urlHead = 'https://api.bjgwbn.net.cn/';
    var data = encryption(config_params);
    $.ajax({
        url: urlHead + 'tool/CreateClue',
        type: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
            data: data
        },
        dataType: 'json',
        success: function (res) {
            if (res.header.code == 200) {
                _alert("感谢您的提供，您的报价已成功生成，我们会尽快联系您。")
                $('#exampleModal').modal('hide');
                $('#exampleModalDrpecn').modal('hide');
            } else {
                _alert(res.body)
                $('#exampleModal').modal('hide');
                $('#exampleModalDrpecn').modal('hide');
            }
        },
        error: function (err) {
            console.log(err);
        }
    })
}
/* API加密 */
function encryption(obj) {
    var appid = obj.appid,
        key = obj.key,
        params_en = JSON.stringify(obj.params),
        sign = (md5(appid + params_en + key)).toString(),
        p_data = {
            'header': {
                'appid': appid,
                'sign': sign.toUpperCase(),
            },
            'body': params_en
        },
        encry_data = Base64.encode(JSON.stringify(p_data));
    return encry_data;
}
